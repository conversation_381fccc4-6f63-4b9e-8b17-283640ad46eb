import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable text output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays text data with various formatting and interaction options
class ThisTextOutput extends StatelessWidget {
  final String id;
  final String label;
  final String value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool selectable;
  final String? helpText;
  final int? maxLines;
  final TextOverflow overflow;
  final TextAlign textAlign;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;

  const ThisTextOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.selectable = true,
    this.helpText,
    this.maxLines,
    this.overflow = TextOverflow.ellipsis,
    this.textAlign = TextAlign.start,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
  });

  void _copyToClipboard(BuildContext context) {
    if (value.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: value));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value.isEmpty ? (placeholder ?? '') : value;
    final isEmpty = value.isEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: Row(
              children: [
                // Prefix Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],

                // Text Content
                Expanded(
                  child: selectable
                      ? SelectableText(
                          displayValue,
                          style: customTextStyle ??
                              LexendTextStyles.lexend14Regular.copyWith(
                                color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                              ),
                          maxLines: maxLines,
                          textAlign: textAlign,
                        )
                      : Text(
                          displayValue,
                          style: customTextStyle ??
                              LexendTextStyles.lexend14Regular.copyWith(
                                color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                              ),
                          maxLines: maxLines,
                          overflow: overflow,
                          textAlign: textAlign,
                        ),
                ),

                // Copy Button
                if (copyable && value.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],

                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A specialized version of ThisTextOutput for displaying formatted data
class ThisFormattedTextOutput extends ThisTextOutput {
  final String Function(String)? formatter;

  const ThisFormattedTextOutput({
    super.key,
    required super.id,
    required super.label,
    required super.value,
    this.formatter,
    super.placeholder,
    super.showLabel,
    super.copyable,
    super.selectable,
    super.helpText,
    super.maxLines,
    super.overflow,
    super.textAlign,
    super.customTextStyle,
    super.customLabelStyle,
    super.prefixIcon,
    super.suffixIcon,
    super.padding,
    super.backgroundColor,
    super.borderRadius,
    super.border,
    super.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final formattedValue = formatter != null && value.isNotEmpty ? formatter!(value) : value;

    return ThisTextOutput(
      id: id,
      label: label,
      value: formattedValue,
      placeholder: placeholder,
      showLabel: showLabel,
      copyable: copyable,
      selectable: selectable,
      helpText: helpText,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      customTextStyle: customTextStyle,
      customLabelStyle: customLabelStyle,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      padding: padding,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      border: border,
      onTap: onTap,
    );
  }
}
