import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// File model for file input
class SelectedFile {
  final String name;
  final String path;
  final int size;
  final String? extension;
  final DateTime dateModified;

  const SelectedFile({
    required this.name,
    required this.path,
    required this.size,
    this.extension,
    required this.dateModified,
  });

  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

/// A customizable file input widget following the 'this_componentName_input' naming convention
/// This widget handles file upload with validation based on API configuration
class ThisFileInput extends StatefulWidget {
  final String id;
  final String label;
  final String? placeholder;
  final List<SelectedFile> value;
  final ValueChanged<List<SelectedFile>> onChanged;
  final ValueChanged<List<String>>? onValidation;
  final bool required;
  final bool disabled;
  final bool readOnly;
  final String? helpText;
  
  // API-based validation parameters
  final String? validationPattern;
  final String? requiredErrorMessage;
  final String? patternErrorMessage;
  final String? fileTypeErrorMessage;
  final String? fileSizeErrorMessage;
  
  // File-specific parameters from API
  final bool allowsMultiple;
  final List<String>? allowedFileTypes; // e.g., ['pdf', 'jpg', 'png'] or ['*'] for all
  final int? maxFileSizeBytes;
  final int? maxSelections;
  final bool showIcon;
  final bool showValidationIcon;
  final bool validateOnBlur;
  final bool autoFocus;
  final String? Function(List<SelectedFile>)? customValidation;

  const ThisFileInput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    required this.onChanged,
    this.placeholder,
    this.onValidation,
    this.required = false,
    this.disabled = false,
    this.readOnly = false,
    this.helpText,
    this.validationPattern,
    this.requiredErrorMessage,
    this.patternErrorMessage,
    this.fileTypeErrorMessage,
    this.fileSizeErrorMessage,
    this.allowsMultiple = false,
    this.allowedFileTypes,
    this.maxFileSizeBytes,
    this.maxSelections,
    this.showIcon = true,
    this.showValidationIcon = true,
    this.validateOnBlur = true,
    this.autoFocus = false,
    this.customValidation,
  });

  @override
  State<ThisFileInput> createState() => _ThisFileInputState();
}

class _ThisFileInputState extends State<ThisFileInput> {
  late FocusNode _focusNode;
  List<String> _errors = [];
  bool _isValidated = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  List<String> get _allowedExtensions {
    if (widget.allowedFileTypes == null || widget.allowedFileTypes!.isEmpty) {
      return [];
    }
    if (widget.allowedFileTypes!.contains('*')) {
      return [];
    }
    return widget.allowedFileTypes!.map((type) => type.toLowerCase()).toList();
  }

  List<String> _validateValue(List<SelectedFile> files) {
    final errors = <String>[];

    // 1. Required validation
    if (widget.required && files.isEmpty) {
      errors.add(widget.requiredErrorMessage ?? '${widget.label} is required');
      return errors;
    }

    // Skip other validations if empty and not required
    if (files.isEmpty && !widget.required) {
      return errors;
    }

    // 2. Multiple files validation
    if (!widget.allowsMultiple && files.length > 1) {
      errors.add('Only one file is allowed');
      return errors;
    }

    // 3. Max selections validation
    if (widget.maxSelections != null && files.length > widget.maxSelections!) {
      errors.add('Maximum ${widget.maxSelections} files allowed');
      return errors;
    }

    // 4. File type validation
    if (_allowedExtensions.isNotEmpty) {
      for (final file in files) {
        final extension = file.extension?.toLowerCase();
        if (extension == null || !_allowedExtensions.contains(extension)) {
          errors.add(widget.fileTypeErrorMessage ?? 'Invalid file type: ${file.name}');
          return errors;
        }
      }
    }

    // 5. File size validation
    if (widget.maxFileSizeBytes != null) {
      for (final file in files) {
        if (file.size > widget.maxFileSizeBytes!) {
          errors.add(widget.fileSizeErrorMessage ?? 'File size exceeds limit: ${file.name}');
          return errors;
        }
      }
    }

    // 6. Pattern validation (for file names)
    if (widget.validationPattern != null) {
      final regex = RegExp(widget.validationPattern!);
      for (final file in files) {
        if (!regex.hasMatch(file.name)) {
          errors.add(widget.patternErrorMessage ?? 'Invalid file name: ${file.name}');
          return errors;
        }
      }
    }

    // 7. Custom validation
    if (widget.customValidation != null) {
      final customError = widget.customValidation!(files);
      if (customError != null) {
        errors.add(customError);
        return errors;
      }
    }

    return errors;
  }

  Future<void> _pickFiles() async {
    if (widget.disabled || widget.readOnly || _isUploading) return;

    setState(() {
      _isUploading = true;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: widget.allowsMultiple,
        type: _allowedExtensions.isEmpty ? FileType.any : FileType.custom,
        allowedExtensions: _allowedExtensions.isEmpty ? null : _allowedExtensions,
      );

      if (result != null) {
        final selectedFiles = result.files.map((file) {
          final fileObj = File(file.path!);
          return SelectedFile(
            name: file.name,
            path: file.path!,
            size: file.size,
            extension: file.extension,
            dateModified: fileObj.lastModifiedSync(),
          );
        }).toList();

        List<SelectedFile> newFiles;
        if (widget.allowsMultiple) {
          newFiles = [...widget.value, ...selectedFiles];
        } else {
          newFiles = selectedFiles;
        }

        widget.onChanged(newFiles);

        // Validate
        final errors = _validateValue(newFiles);
        setState(() {
          _errors = errors;
          _isValidated = true;
        });
        
        widget.onValidation?.call(errors);
      }
    } catch (e) {
      setState(() {
        _errors = ['Error picking files: $e'];
        _isValidated = true;
      });
      widget.onValidation?.call(_errors);
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _removeFile(int index) {
    final newFiles = List<SelectedFile>.from(widget.value);
    newFiles.removeAt(index);
    widget.onChanged(newFiles);

    // Validate
    final errors = _validateValue(newFiles);
    setState(() {
      _errors = errors;
      _isValidated = true;
    });
    
    widget.onValidation?.call(errors);
  }

  void _handleBlur() {
    if (widget.validateOnBlur) {
      final errors = _validateValue(widget.value);
      setState(() {
        _errors = errors;
        _isValidated = widget.value.isNotEmpty;
      });
      
      widget.onValidation?.call(errors);
    }
  }

  Widget? _getValidationIcon() {
    if (!widget.showValidationIcon || !_isValidated || widget.value.isEmpty) {
      return null;
    }

    final hasErrors = _errors.isNotEmpty;
    return Icon(
      hasErrors ? Icons.close : Icons.check,
      size: 16,
      color: hasErrors ? const Color(0xFFC73E1D) : ColorPalette.green,
    );
  }

  String _getDisplayText() {
    if (widget.value.isEmpty) {
      return widget.placeholder ?? 'Choose file${widget.allowsMultiple ? 's' : ''}...';
    }

    if (widget.value.length == 1) {
      return widget.value.first.name;
    } else {
      return '${widget.value.length} files selected';
    }
  }

  String _getAllowedTypesText() {
    if (_allowedExtensions.isEmpty) {
      return 'All file types allowed';
    }
    return 'Allowed: ${_allowedExtensions.join(', ').toUpperCase()}';
  }

  String _getMaxSizeText() {
    if (widget.maxFileSizeBytes == null) return '';
    
    final maxSize = widget.maxFileSizeBytes!;
    if (maxSize < 1024) return 'Max size: ${maxSize}B';
    if (maxSize < 1024 * 1024) return 'Max size: ${(maxSize / 1024).toStringAsFixed(1)}KB';
    if (maxSize < 1024 * 1024 * 1024) return 'Max size: ${(maxSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return 'Max size: ${(maxSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = _errors.isNotEmpty;
    final isValid = _isValidated && !hasErrors && widget.value.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Text(
              widget.label,
              style: LexendTextStyles.lexend14Medium.copyWith(
                color: widget.disabled 
                    ? ColorPalette.placeHolderTextColor 
                    : ColorPalette.white,
              ),
            ),
            if (widget.required)
              Text(
                ' *',
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: const Color(0xFFC73E1D),
                ),
              ),
            if (widget.helpText != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.helpText!,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorPalette.placeHolderTextColor,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        
        // File picker button
        GestureDetector(
          onTap: _pickFiles,
          onFocusChange: (focused) {
            if (!focused) _handleBlur();
          },
          child: Focus(
            focusNode: _focusNode,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: hasErrors 
                      ? const Color(0xFFC73E1D)
                      : (isValid ? ColorPalette.green : ColorPalette.gray300),
                  style: BorderStyle.dashed,
                ),
                borderRadius: BorderRadius.circular(6),
                color: _isUploading 
                    ? ColorPalette.gray300.withValues(alpha: 0.1)
                    : null,
              ),
              child: Column(
                children: [
                  if (_isUploading) ...[
                    const CircularProgressIndicator(),
                    const SizedBox(height: 8),
                    Text(
                      'Uploading...',
                      style: LexendTextStyles.lexend14Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ] else ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (widget.showIcon) ...[
                          Icon(
                            Icons.cloud_upload,
                            size: 24,
                            color: ColorPalette.placeHolderTextColor,
                          ),
                          const SizedBox(width: 8),
                        ],
                        Expanded(
                          child: Text(
                            _getDisplayText(),
                            style: LexendTextStyles.lexend14Regular.copyWith(
                              color: widget.value.isEmpty
                                  ? ColorPalette.placeHolderTextColor
                                  : ColorPalette.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        if (_getValidationIcon() != null) _getValidationIcon()!,
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to select file${widget.allowsMultiple ? 's' : ''}',
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
        
        // Selected files list
        if (widget.value.isNotEmpty) ...[
          const SizedBox(height: 12),
          ...widget.value.asMap().entries.map((entry) {
            final index = entry.key;
            final file = entry.value;
            
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorPalette.darkToneInk.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: ColorPalette.gray300.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.insert_drive_file,
                    size: 20,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          file.name,
                          style: LexendTextStyles.lexend14Regular.copyWith(
                            color: ColorPalette.white,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          file.sizeFormatted,
                          style: LexendTextStyles.lexend12Regular.copyWith(
                            color: ColorPalette.placeHolderTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!widget.disabled && !widget.readOnly)
                    IconButton(
                      icon: const Icon(Icons.close, size: 16),
                      color: const Color(0xFFC73E1D),
                      onPressed: () => _removeFile(index),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                ],
              ),
            );
          }),
        ],
        
        // Error message
        if (hasErrors)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _errors.first,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: const Color(0xFFC73E1D),
              ),
            ),
          ),
        
        // Helper text
        if (!hasErrors) ...[
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getAllowedTypesText(),
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
                if (widget.maxFileSizeBytes != null)
                  Text(
                    _getMaxSizeText(),
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                if (widget.allowsMultiple && widget.maxSelections != null)
                  Text(
                    'Maximum ${widget.maxSelections} files',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
