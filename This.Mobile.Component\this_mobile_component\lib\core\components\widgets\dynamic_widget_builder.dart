import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

/// Dynamic widget builder that creates widgets based on API configuration
class DynamicWidgetBuilder {
  /// Build a widget based on API component configuration
  static Widget buildWidget({
    required ApiComponentModel config,
    required dynamic value,
    required ValueChanged<dynamic> onChanged,
    ValueChanged<List<String>>? onValidation,
    String? helpText,
  }) {
    // Check if component should be rendered
    if (!config.shouldRender) {
      return const SizedBox.shrink();
    }

    // Use helpText parameter or fall back to placeholder from API
    final effectiveHelpText = helpText ?? config.placeholder;

    switch (config.widgetType) {
      case WidgetType.text:
        return ThisTextInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          inputMask: config.inputMask,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
        );

      case WidgetType.textarea:
        return ThisTextareaInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
        );

      case WidgetType.email:
        return ThisEmailInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
        );

      case WidgetType.phone:
        return ThisPhoneInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
        );

      case WidgetType.number:
        return ThisNumberInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          minValue: config.minValue,
          maxValue: config.maxValue,
          decimalPlaces: config.decimalPlaces,
          stepValue: config.stepValue,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
          minValueErrorMessage: config.minValueErrorMessage,
          maxValueErrorMessage: config.maxValueErrorMessage,
        );

      case WidgetType.currency:
        return ThisCurrencyInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          minValue: config.minValue,
          maxValue: config.maxValue,
          decimalPlaces: config.decimalPlaces,
          stepValue: config.stepValue,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
          minValueErrorMessage: config.minValueErrorMessage,
          maxValueErrorMessage: config.maxValueErrorMessage,
        );

      case WidgetType.percentage:
        return ThisPercentageInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          minValue: config.minValue,
          maxValue: config.maxValue,
          decimalPlaces: config.decimalPlaces,
          stepValue: config.stepValue,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
          minValueErrorMessage: config.minValueErrorMessage,
          maxValueErrorMessage: config.maxValueErrorMessage,
        );

      case WidgetType.dropdown:
        return ThisDropdownInput(
          id: config.id,
          label: config.label,
          options: config.dropdownOptions,
          value: value,
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          allowsMultiple: config.allowsMultiple,
          allowsCustomOptions: config.allowsCustomOptions,
          maxSelections: config.maxSelections,
          validationPattern: config.validationPattern,
          minLength: config.minLength,
          maxLength: config.maxLength,
          requiredErrorMessage: config.requiredErrorMessage,
          patternErrorMessage: config.patternErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
        );

      case WidgetType.checkbox:
        return ThisCheckboxInput(
          id: config.id,
          label: config.label,
          options: config.dropdownOptions
              .map((opt) => CheckboxOption(
                    value: opt.value,
                    label: opt.label,
                  ))
              .toList(),
          value: value is List<String> ? value : [],
          onChanged: onChanged,
          onValidation: onValidation,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          allowsMultiple: config.allowsMultiple,
          maxSelections: config.maxSelections,
          minSelected: config.minLength,
          requiredErrorMessage: config.requiredErrorMessage,
          minLengthErrorMessage: config.minLengthErrorMessage,
          maxLengthErrorMessage: config.maxLengthErrorMessage,
        );

      case WidgetType.radio:
        return ThisRadioInput(
          id: config.id,
          label: config.label,
          options: config.dropdownOptions
              .map((opt) => RadioOption(
                    value: opt.value,
                    label: opt.label,
                  ))
              .toList(),
          value: value?.toString(),
          onChanged: onChanged,
          onValidation: onValidation,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          requiredErrorMessage: config.requiredErrorMessage,
        );

      case WidgetType.slider:
        return ThisSliderInput(
          id: config.id,
          label: config.label,
          value: (value is num) ? value.toDouble() : 0.0,
          onChanged: onChanged,
          onValidation: onValidation,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          minValue: config.minValue,
          maxValue: config.maxValue,
          decimalPlaces: config.decimalPlaces,
          stepValue: config.stepValue,
          requiredErrorMessage: config.requiredErrorMessage,
          minValueErrorMessage: config.minValueErrorMessage,
          maxValueErrorMessage: config.maxValueErrorMessage,
        );

      case WidgetType.file:
        return ThisFileInput(
          id: config.id,
          label: config.label,
          value: value is List<SelectedFile> ? value : [],
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          allowsMultiple: config.allowsMultiple,
          allowedFileTypes: config.allowedFileTypes,
          maxFileSizeBytes: config.maxFileSizeBytes,
          maxSelections: config.maxSelections,
          requiredErrorMessage: config.requiredErrorMessage,
          fileTypeErrorMessage: config.fileTypeErrorMessage,
          fileSizeErrorMessage: config.fileSizeErrorMessage,
        );

      case WidgetType.image:
        return ThisImageInput(
          id: config.id,
          label: config.label,
          value: value is List<SelectedImage> ? value : [],
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          allowsMultiple: config.allowsMultiple,
          allowedFileTypes: config.allowedFileTypes,
          maxFileSizeBytes: config.maxFileSizeBytes,
          maxSelections: config.maxSelections,
          requiredErrorMessage: config.requiredErrorMessage,
          fileTypeErrorMessage: config.fileTypeErrorMessage,
          fileSizeErrorMessage: config.fileSizeErrorMessage,
        );

      case WidgetType.video:
        return ThisVideoInput(
          id: config.id,
          label: config.label,
          value: value is List<SelectedVideo> ? value : [],
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          allowsMultiple: config.allowsMultiple,
          allowedFileTypes: config.allowedFileTypes,
          maxFileSizeBytes: config.maxFileSizeBytes,
          maxSelections: config.maxSelections,
          requiredErrorMessage: config.requiredErrorMessage,
          fileTypeErrorMessage: config.fileTypeErrorMessage,
          fileSizeErrorMessage: config.fileSizeErrorMessage,
        );

      case WidgetType.year:
        return ThisYearInput(
          id: config.id,
          label: config.label,
          value: value is int ? value : null,
          onChanged: onChanged,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          minYear: config.minValue?.toInt(),
          maxYear: config.maxValue?.toInt(),
          requiredErrorMessage: config.requiredErrorMessage,
          minValueErrorMessage: config.minValueErrorMessage,
          maxValueErrorMessage: config.maxValueErrorMessage,
        );

      case WidgetType.month:
        return ThisMonthInput(
          id: config.id,
          label: config.label,
          value: value is int ? value : null,
          onChanged: onChanged,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          requiredErrorMessage: config.requiredErrorMessage,
        );

      case WidgetType.day:
        return ThisDayInput(
          id: config.id,
          label: config.label,
          value: value is int ? value : null,
          onChanged: onChanged,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          requiredErrorMessage: config.requiredErrorMessage,
          minValueErrorMessage: config.minValueErrorMessage,
          maxValueErrorMessage: config.maxValueErrorMessage,
        );

      case WidgetType.time:
        return ThisTimeInput(
          id: config.id,
          label: config.label,
          value: value is TimeOfDay ? value : null,
          onChanged: onChanged,
          required: config.isRequired,
          helpText: effectiveHelpText,
          // API parameters
          requiredErrorMessage: config.requiredErrorMessage,
        );

      // Add other widget types as needed
      default:
        return ThisTextInput(
          id: config.id,
          label: config.label,
          value: value?.toString() ?? '',
          onChanged: onChanged,
          onValidation: onValidation,
          placeholder: config.placeholder,
          required: config.isRequired,
          helpText: effectiveHelpText,
        );
    }
  }

  /// Build multiple widgets from a list of API configurations
  static List<Widget> buildWidgets({
    required List<ApiComponentModel> configs,
    required Map<String, dynamic> values,
    required ValueChanged<String> onChanged,
    ValueChanged<Map<String, List<String>>>? onValidation,
  }) {
    return configs.map((config) {
      return buildWidget(
        config: config,
        value: values[config.id],
        onChanged: (value) => onChanged(config.id),
        onValidation: onValidation != null ? (errors) => onValidation({config.id: errors}) : null,
      );
    }).toList();
  }
}
