import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widget_enums.dart';

/// A customizable time output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays time data with various formatting options
class ThisTimeOutput extends StatelessWidget {
  final String id;
  final String label;
  final TimeOfDay? value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final TimeDisplayFormat displayFormat;
  final bool use24HourFormat;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showRelativeTime;

  const ThisTimeOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.displayFormat = TimeDisplayFormat.HH_MM,
    this.use24HourFormat = true,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showRelativeTime = false,
  });

  String _formatTime(TimeOfDay time) {
    switch (displayFormat) {
      case TimeDisplayFormat.hhMm:
        if (use24HourFormat) {
          return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        } else {
          final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
          final period = time.period == DayPeriod.am ? 'AM' : 'PM';
          return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} $period';
        }
      case TimeDisplayFormat.hhMmSs:
        if (use24HourFormat) {
          return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:00';
        } else {
          final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
          final period = time.period == DayPeriod.am ? 'AM' : 'PM';
          return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:00 $period';
        }
      case TimeDisplayFormat.descriptive:
        return _getDescriptiveTime(time);
    }
  }

  String _getDescriptiveTime(TimeOfDay time) {
    final hour = time.hour;
    final minute = time.minute;

    if (minute == 0) {
      if (hour == 0) return 'Midnight';
      if (hour == 12) return 'Noon';
      if (use24HourFormat) {
        return '${hour.toString().padLeft(2, '0')}:00';
      } else {
        final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
        final period = hour >= 12 ? 'PM' : 'AM';
        return '$displayHour:00 $period';
      }
    } else if (minute == 15) {
      return 'Quarter past ${_getHourName(hour)}';
    } else if (minute == 30) {
      return 'Half past ${_getHourName(hour)}';
    } else if (minute == 45) {
      return 'Quarter to ${_getHourName(hour + 1)}';
    } else {
      return _formatTime(time); // Fall back to regular format
    }
  }

  String _getHourName(int hour) {
    hour = hour % 24; // Handle overflow
    if (hour == 0) return 'midnight';
    if (hour == 12) return 'noon';

    if (use24HourFormat) {
      return hour.toString();
    } else {
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      return displayHour.toString();
    }
  }

  String _getRelativeTime(TimeOfDay time) {
    final now = TimeOfDay.now();
    final nowMinutes = now.hour * 60 + now.minute;
    final timeMinutes = time.hour * 60 + time.minute;
    final difference = timeMinutes - nowMinutes;

    if (difference == 0) {
      return 'Now';
    } else if (difference > 0) {
      if (difference < 60) {
        return 'In $difference minute${difference > 1 ? 's' : ''}';
      } else {
        final hours = difference ~/ 60;
        final minutes = difference % 60;
        if (minutes == 0) {
          return 'In $hours hour${hours > 1 ? 's' : ''}';
        } else {
          return 'In ${hours}h ${minutes}m';
        }
      }
    } else {
      final absDifference = difference.abs();
      if (absDifference < 60) {
        return '$absDifference minute${absDifference > 1 ? 's' : ''} ago';
      } else {
        final hours = absDifference ~/ 60;
        final minutes = absDifference % 60;
        if (minutes == 0) {
          return '$hours hour${hours > 1 ? 's' : ''} ago';
        } else {
          return '${hours}h ${minutes}m ago';
        }
      }
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (value != null) {
      Clipboard.setData(ClipboardData(text: _formatTime(value!)));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value != null ? _formatTime(value!) : (placeholder ?? '');
    final isEmpty = value == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: Row(
              children: [
                // Prefix Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],

                // Time Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayValue,
                        style: customTextStyle ??
                            LexendTextStyles.lexend14Regular.copyWith(
                              color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                            ),
                      ),
                      if (showRelativeTime && value != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: Text(
                            _getRelativeTime(value!),
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.placeHolderTextColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Copy Button
                if (copyable && value != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],

                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A specialized version of ThisTimeOutput for displaying time ranges
class ThisTimeRangeOutput extends StatelessWidget {
  final String id;
  final String label;
  final TimeOfDay? startTime;
  final TimeOfDay? endTime;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final String separator;
  final TimeDisplayFormat displayFormat;
  final bool use24HourFormat;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;

  const ThisTimeRangeOutput({
    super.key,
    required this.id,
    required this.label,
    required this.startTime,
    required this.endTime,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.separator = ' - ',
    this.displayFormat = TimeDisplayFormat.HH_MM,
    this.use24HourFormat = true,
    this.customTextStyle,
    this.customLabelStyle,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
  });

  String _formatTime(TimeOfDay time) {
    switch (displayFormat) {
      case TimeDisplayFormat.HH_MM:
        if (use24HourFormat) {
          return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
        } else {
          final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
          final period = time.period == DayPeriod.am ? 'AM' : 'PM';
          return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')} $period';
        }
      case TimeDisplayFormat.HH_MM_SS:
        if (use24HourFormat) {
          return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:00';
        } else {
          final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
          final period = time.period == DayPeriod.am ? 'AM' : 'PM';
          return '${hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:00 $period';
        }
      case TimeDisplayFormat.DESCRIPTIVE:
        return _formatTime(time); // Use regular format for ranges
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (startTime != null && endTime != null) {
      final text = '${_formatTime(startTime!)}$separator${_formatTime(endTime!)}';
      Clipboard.setData(ClipboardData(text: text));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String displayValue;
    bool isEmpty;

    if (startTime != null && endTime != null) {
      displayValue = '${_formatTime(startTime!)}$separator${_formatTime(endTime!)}';
      isEmpty = false;
    } else if (startTime != null) {
      displayValue = '${_formatTime(startTime!)}$separator?';
      isEmpty = false;
    } else if (endTime != null) {
      displayValue = '?$separator${_formatTime(endTime!)}';
      isEmpty = false;
    } else {
      displayValue = placeholder ?? '';
      isEmpty = true;
    }

    return ThisTimeOutput(
      id: id,
      label: label,
      value: null, // We handle display manually
      placeholder: displayValue,
      showLabel: showLabel,
      copyable: false, // We handle copy manually
      helpText: helpText,
      displayFormat: displayFormat,
      use24HourFormat: use24HourFormat,
      customTextStyle: customTextStyle,
      customLabelStyle: customLabelStyle,
      padding: padding,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      border: border,
      onTap: onTap,
      suffixIcon: copyable && !isEmpty
          ? IconButton(
              icon: const Icon(Icons.copy, size: 16),
              onPressed: () => _copyToClipboard(context),
              color: ColorPalette.placeHolderTextColor,
              tooltip: 'Copy to clipboard',
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: const EdgeInsets.all(4),
            )
          : null,
    );
  }
}
