name: this_mobile_component
description: "A new Flutter project."

version: 1.0.0+1

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  url_launcher: ^6.2.2
  intl: ^0.19.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/prospect/
    - assets/icons/
    - assets/lottie/
    - assets/config/.env

  fonts:
    - family: LexendDecaThin
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Thin.ttf
          weight: 100
          style: normal

    - family: LexendDecaRegular
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Regular.ttf
          weight: 400
          style: normal

    - family: LexendDecaMedium
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Medium.ttf
          weight: 500
          style: normal

    - family: LexendDecaSemiBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-SemiBold.ttf
          weight: 600
          style: normal

    - family: LexendDecaBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Bold.ttf
          weight: 700
          style: normal

    - family: LexendDecaExtraBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-ExtraBold.ttf
          weight: 800
          style: normal

    - family: LexendDecaBlack
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Black.ttf
          weight: 900
          style: normal

    - family: LexendDecaLight
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Light.ttf
          weight: 300
          style: normal

    - family: LexendDecaExtraLight
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-ExtraLight.ttf
          weight: 200
          style: normal
