import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widget_enums.dart';

/// A customizable year output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays year data with various formatting options
class ThisYearOutput extends StatelessWidget {
  final String id;
  final String label;
  final int? value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final YearDisplayFormat displayFormat;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showRelativeInfo;

  const ThisYearOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.displayFormat = YearDisplayFormat.full,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showRelativeInfo = false,
  });

  String _formatYear(int year) {
    switch (displayFormat) {
      case YearDisplayFormat.full:
        return year.toString();
      case YearDisplayFormat.short:
        return year.toString().substring(2); // Last 2 digits
      case YearDisplayFormat.withSuffix:
        return '$year AD';
    }
  }

  String _getRelativeInfo(int year) {
    final currentYear = DateTime.now().year;
    final difference = year - currentYear;

    if (difference == 0) {
      return 'Current year';
    } else if (difference > 0) {
      return '$difference year${difference > 1 ? 's' : ''} from now';
    } else {
      return '${difference.abs()} year${difference.abs() > 1 ? 's' : ''} ago';
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (value != null) {
      Clipboard.setData(ClipboardData(text: value.toString()));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value != null ? _formatYear(value!) : (placeholder ?? '');
    final isEmpty = value == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: Row(
              children: [
                // Prefix Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],

                // Year Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayValue,
                        style: customTextStyle ??
                            LexendTextStyles.lexend14Regular.copyWith(
                              color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                            ),
                      ),
                      if (showRelativeInfo && value != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: Text(
                            _getRelativeInfo(value!),
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.placeHolderTextColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Copy Button
                if (copyable && value != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],

                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A specialized version of ThisYearOutput for displaying year ranges
class ThisYearRangeOutput extends StatelessWidget {
  final String id;
  final String label;
  final int? startYear;
  final int? endYear;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final String separator;
  final YearDisplayFormat displayFormat;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;

  const ThisYearRangeOutput({
    super.key,
    required this.id,
    required this.label,
    required this.startYear,
    required this.endYear,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.separator = ' - ',
    this.displayFormat = YearDisplayFormat.full,
    this.customTextStyle,
    this.customLabelStyle,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
  });

  String _formatYear(int year) {
    switch (displayFormat) {
      case YearDisplayFormat.full:
        return year.toString();
      case YearDisplayFormat.short:
        return year.toString().substring(2);
      case YearDisplayFormat.withSuffix:
        return '$year AD';
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (startYear != null && endYear != null) {
      final text = '${_formatYear(startYear!)}$separator${_formatYear(endYear!)}';
      Clipboard.setData(ClipboardData(text: text));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String displayValue;
    bool isEmpty;

    if (startYear != null && endYear != null) {
      displayValue = '${_formatYear(startYear!)}$separator${_formatYear(endYear!)}';
      isEmpty = false;
    } else if (startYear != null) {
      displayValue = '${_formatYear(startYear!)}$separator?';
      isEmpty = false;
    } else if (endYear != null) {
      displayValue = '?$separator${_formatYear(endYear!)}';
      isEmpty = false;
    } else {
      displayValue = placeholder ?? '';
      isEmpty = true;
    }

    return ThisYearOutput(
      id: id,
      label: label,
      value: null, // We handle display manually
      placeholder: displayValue,
      showLabel: showLabel,
      copyable: false, // We handle copy manually
      helpText: helpText,
      customTextStyle: customTextStyle,
      customLabelStyle: customLabelStyle,
      padding: padding,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      border: border,
      onTap: onTap,
      suffixIcon: copyable && !isEmpty
          ? IconButton(
              icon: const Icon(Icons.copy, size: 16),
              onPressed: () => _copyToClipboard(context),
              color: ColorPalette.placeHolderTextColor,
              tooltip: 'Copy to clipboard',
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: const EdgeInsets.all(4),
            )
          : null,
    );
  }
}
