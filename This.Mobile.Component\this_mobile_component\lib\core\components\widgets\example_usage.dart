import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

/// Example usage of all the custom widgets following the 'this_componentName_relatedTo' naming convention
/// This demonstrates how to use both input and output widgets with various configurations
class WidgetExamplePage extends StatefulWidget {
  const WidgetExamplePage({super.key});

  @override
  State<WidgetExamplePage> createState() => _WidgetExamplePageState();
}

class _WidgetExamplePageState extends State<WidgetExamplePage> {
  // Form state variables
  String _textValue = '';
  String _textareaValue = '';
  List<String> _checkboxValues = [];
  String? _radioValue;
  int? _yearValue;
  int? _monthValue;
  int? _dayValue;
  TimeOfDay? _timeValue;

  // Sample data for options
  final List<CheckboxOption> _checkboxOptions = [
    const CheckboxOption(value: 'option1', label: 'Option 1'),
    const CheckboxOption(value: 'option2', label: 'Option 2'),
    const CheckboxOption(value: 'option3', label: 'Option 3'),
  ];

  final List<RadioOption> _radioOptions = [
    const RadioOption(value: 'choice1', label: 'Choice 1', description: 'First choice'),
    const RadioOption(value: 'choice2', label: 'Choice 2', description: 'Second choice'),
    const RadioOption(value: 'choice3', label: 'Choice 3', description: 'Third choice'),
  ];

  final List<CheckboxDisplayOption> _checkboxDisplayOptions = [
    const CheckboxDisplayOption(value: 'option1', label: 'Option 1'),
    const CheckboxDisplayOption(value: 'option2', label: 'Option 2'),
    const CheckboxDisplayOption(value: 'option3', label: 'Option 3'),
  ];

  final List<RadioDisplayOption> _radioDisplayOptions = [
    const RadioDisplayOption(value: 'choice1', label: 'Choice 1', description: 'First choice'),
    const RadioDisplayOption(value: 'choice2', label: 'Choice 2', description: 'Second choice'),
    const RadioDisplayOption(value: 'choice3', label: 'Choice 3', description: 'Third choice'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Widget Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Text Input/Output Examples
            _buildSection(
              'Text Components',
              [
                ThisTextInput(
                  id: 'text_input_example',
                  label: 'Text Input',
                  value: _textValue,
                  onChanged: (value) => setState(() => _textValue = value),
                  placeholder: 'Enter some text...',
                  required: true,
                  showCharacterCount: true,
                  maxLength: 100,
                  allowClear: true,
                ),
                const SizedBox(height: 16),
                ThisTextOutput(
                  id: 'text_output_example',
                  label: 'Text Output',
                  value: _textValue.isEmpty ? 'Sample output text' : _textValue,
                  copyable: true,
                  prefixIcon: const Icon(Icons.text_fields, size: 16),
                ),
              ],
            ),

            // Textarea Input/Output Examples
            _buildSection(
              'Textarea Components',
              [
                ThisTextareaInput(
                  id: 'textarea_input_example',
                  label: 'Textarea Input',
                  value: _textareaValue,
                  onChanged: (value) => setState(() => _textareaValue = value),
                  placeholder: 'Enter multiple lines of text...',
                  minLines: 3,
                  maxLines: 6,
                  showCharacterCount: true,
                  maxLength: 500,
                ),
                const SizedBox(height: 16),
                ThisTextareaOutput(
                  id: 'textarea_output_example',
                  label: 'Textarea Output',
                  value: _textareaValue.isEmpty ? 'Sample multi-line output text\nSecond line\nThird line' : _textareaValue,
                  copyable: true,
                  showWordCount: true,
                  expandable: true,
                ),
              ],
            ),

            // Checkbox Input/Output Examples
            _buildSection(
              'Checkbox Components',
              [
                ThisCheckboxInput(
                  id: 'checkbox_input_example',
                  label: 'Checkbox Input',
                  options: _checkboxOptions,
                  value: _checkboxValues,
                  onChanged: (values) => setState(() => _checkboxValues = values),
                  allowSelectAll: true,
                  required: true,
                  minSelected: 1,
                ),
                const SizedBox(height: 16),
                ThisCheckboxOutput(
                  id: 'checkbox_output_example',
                  label: 'Checkbox Output',
                  options: _checkboxDisplayOptions,
                  value: _checkboxValues.isEmpty ? ['option1', 'option3'] : _checkboxValues,
                  displayFormat: DisplayFormat.chips,
                  showCheckmarks: true,
                ),
              ],
            ),

            // Radio Input/Output Examples
            _buildSection(
              'Radio Components',
              [
                ThisRadioInput(
                  id: 'radio_input_example',
                  label: 'Radio Input',
                  options: _radioOptions,
                  value: _radioValue,
                  onChanged: (value) => setState(() => _radioValue = value),
                  required: true,
                ),
                const SizedBox(height: 16),
                ThisRadioOutput(
                  id: 'radio_output_example',
                  label: 'Radio Output',
                  options: _radioDisplayOptions,
                  value: _radioValue ?? 'choice2',
                  displayStyle: DisplayStyle.detailed,
                  showDescription: true,
                ),
              ],
            ),

            // Year Input/Output Examples
            _buildSection(
              'Year Components',
              [
                ThisYearInput(
                  id: 'year_input_example',
                  label: 'Year Input',
                  value: _yearValue,
                  onChanged: (value) => setState(() => _yearValue = value),
                  showDropdown: true,
                  minYear: 2000,
                  maxYear: 2030,
                  required: true,
                ),
                const SizedBox(height: 16),
                ThisYearOutput(
                  id: 'year_output_example',
                  label: 'Year Output',
                  value: _yearValue ?? 2024,
                  showRelativeInfo: true,
                  copyable: true,
                ),
              ],
            ),

            // Month Input/Output Examples
            _buildSection(
              'Month Components',
              [
                ThisMonthInput(
                  id: 'month_input_example',
                  label: 'Month Input',
                  value: _monthValue,
                  onChanged: (value) => setState(() => _monthValue = value),
                  displayFormat: MonthDisplayFormat.full,
                  required: true,
                ),
                const SizedBox(height: 16),
                ThisMonthOutput(
                  id: 'month_output_example',
                  label: 'Month Output',
                  value: _monthValue ?? DateTime.now().month,
                  displayFormat: MonthDisplayFormat.full,
                  showMonthInfo: true,
                  copyable: true,
                ),
              ],
            ),

            // Day Input/Output Examples
            _buildSection(
              'Day Components',
              [
                ThisDayInput(
                  id: 'day_input_example',
                  label: 'Day Input',
                  value: _dayValue,
                  onChanged: (value) => setState(() => _dayValue = value),
                  month: _monthValue,
                  year: _yearValue,
                  showDropdown: true,
                  required: true,
                ),
                const SizedBox(height: 16),
                ThisDayOutput(
                  id: 'day_output_example',
                  label: 'Day Output',
                  value: _dayValue ?? DateTime.now().day,
                  displayFormat: DayDisplayFormat.ordinal,
                  month: _monthValue ?? DateTime.now().month,
                  year: _yearValue ?? DateTime.now().year,
                  showDayInfo: true,
                  copyable: true,
                ),
              ],
            ),

            // Time Input/Output Examples
            _buildSection(
              'Time Components',
              [
                ThisTimeInput(
                  id: 'time_input_example',
                  label: 'Time Input',
                  value: _timeValue,
                  onChanged: (value) => setState(() => _timeValue = value),
                  use24HourFormat: false,
                  showTimePicker: true,
                  required: true,
                ),
                const SizedBox(height: 16),
                ThisTimeOutput(
                  id: 'time_output_example',
                  label: 'Time Output',
                  value: _timeValue ?? TimeOfDay.now(),
                  use24HourFormat: false,
                  showRelativeTime: true,
                  copyable: true,
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[700]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
