import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Display format for email output widgets
enum EmailDisplayFormat {
  full,        // <EMAIL>
  masked,      // u***@example.com
  domainOnly,  // example.com
  localOnly,   // user
}

/// A customizable email output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays email data with various formatting and interaction options
class ThisEmailOutput extends StatelessWidget {
  final String id;
  final String label;
  final String value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool selectable;
  final bool clickable;
  final String? helpText;
  final EmailDisplayFormat displayFormat;
  final bool showIcon;
  final bool showValidationIcon;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool validateEmail;

  const ThisEmailOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.selectable = true,
    this.clickable = false,
    this.helpText,
    this.displayFormat = EmailDisplayFormat.full,
    this.showIcon = true,
    this.showValidationIcon = false,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.validateEmail = true,
  });

  // Email validation regex
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9.!#$%&\'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
  );

  String _extractDomain(String email) {
    final parts = email.split('@');
    return parts.length == 2 ? parts[1] : '';
  }

  String _extractLocalPart(String email) {
    final parts = email.split('@');
    return parts.length == 2 ? parts[0] : '';
  }

  bool _isValidEmail(String email) {
    return _emailRegex.hasMatch(email);
  }

  String _formatEmail(String email) {
    if (email.isEmpty) return '';

    switch (displayFormat) {
      case EmailDisplayFormat.full:
        return email;
      case EmailDisplayFormat.masked:
        if (!_isValidEmail(email)) return email;
        final localPart = _extractLocalPart(email);
        final domain = _extractDomain(email);
        if (localPart.length <= 2) {
          return '${localPart[0]}***@$domain';
        } else {
          return '${localPart[0]}${'*' * (localPart.length - 2)}${localPart[localPart.length - 1]}@$domain';
        }
      case EmailDisplayFormat.domainOnly:
        return _extractDomain(email);
      case EmailDisplayFormat.localOnly:
        return _extractLocalPart(email);
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (value.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: value));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Email copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  Future<void> _launchEmail(BuildContext context) async {
    if (value.isEmpty || !_isValidEmail(value)) return;

    final emailUri = Uri(
      scheme: 'mailto',
      path: value,
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Cannot open email client',
                style: LexendTextStyles.lexend12Regular.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              backgroundColor: const Color(0xFFC73E1D),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error opening email client',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.white,
              ),
            ),
            backgroundColor: const Color(0xFFC73E1D),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        );
      }
    }
  }

  Widget? _getValidationIcon() {
    if (!showValidationIcon || value.isEmpty) return null;

    final isValid = validateEmail ? _isValidEmail(value) : true;
    return Icon(
      isValid ? Icons.check_circle : Icons.error,
      size: 16,
      color: isValid ? ColorPalette.green : const Color(0xFFC73E1D),
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value.isEmpty ? (placeholder ?? '') : _formatEmail(value);
    final isEmpty = value.isEmpty;
    final isValid = validateEmail ? _isValidEmail(value) : true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ?? LexendTextStyles.lexend14Medium.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        
        // Content Container
        GestureDetector(
          onTap: onTap ?? (clickable ? () => _launchEmail(context) : null),
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withValues(alpha: 0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ?? Border.all(
                color: validateEmail && !isEmpty
                    ? (isValid ? ColorPalette.green.withValues(alpha: 0.3) : const Color(0xFFC73E1D).withValues(alpha: 0.3))
                    : ColorPalette.gray300.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Prefix Icon or Email Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ] else if (showIcon) ...[
                  Icon(
                    Icons.email,
                    size: 16,
                    color: isEmpty 
                        ? ColorPalette.placeHolderTextColor 
                        : (isValid ? ColorPalette.white : const Color(0xFFC73E1D)),
                  ),
                  const SizedBox(width: 8),
                ],
                
                // Email Content
                Expanded(
                  child: selectable
                      ? SelectableText(
                          displayValue,
                          style: customTextStyle ?? LexendTextStyles.lexend14Regular.copyWith(
                            color: isEmpty 
                                ? ColorPalette.placeHolderTextColor 
                                : (isValid ? ColorPalette.white : const Color(0xFFC73E1D)),
                            decoration: clickable && !isEmpty ? TextDecoration.underline : null,
                          ),
                        )
                      : Text(
                          displayValue,
                          style: customTextStyle ?? LexendTextStyles.lexend14Regular.copyWith(
                            color: isEmpty 
                                ? ColorPalette.placeHolderTextColor 
                                : (isValid ? ColorPalette.white : const Color(0xFFC73E1D)),
                            decoration: clickable && !isEmpty ? TextDecoration.underline : null,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                ),
                
                // Validation Icon
                if (_getValidationIcon() != null) ...[
                  const SizedBox(width: 8),
                  _getValidationIcon()!,
                ],
                
                // Copy Button
                if (copyable && value.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy email to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],
                
                // Launch Email Button
                if (clickable && value.isNotEmpty && isValid) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.launch, size: 16),
                    onPressed: () => _launchEmail(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Open in email client',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],
                
                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A specialized version of ThisEmailOutput for displaying multiple emails
class ThisEmailListOutput extends StatelessWidget {
  final String id;
  final String label;
  final List<String> values;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool clickable;
  final String? helpText;
  final EmailDisplayFormat displayFormat;
  final bool showIcon;
  final int? maxDisplayItems;
  final String moreItemsText;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;

  const ThisEmailListOutput({
    super.key,
    required this.id,
    required this.label,
    required this.values,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.clickable = false,
    this.helpText,
    this.displayFormat = EmailDisplayFormat.full,
    this.showIcon = true,
    this.maxDisplayItems,
    this.moreItemsText = 'more',
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    final displayValues = maxDisplayItems != null && values.length > maxDisplayItems!
        ? values.take(maxDisplayItems!).toList()
        : values;
    
    final hasMore = maxDisplayItems != null && values.length > maxDisplayItems!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        
        // Email List
        if (values.isEmpty)
          Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withValues(alpha: 0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ?? Border.all(
                color: ColorPalette.gray300.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              placeholder ?? 'No emails',
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          )
        else
          Column(
            children: [
              ...displayValues.asMap().entries.map((entry) {
                final index = entry.key;
                final email = entry.value;
                return Padding(
                  padding: EdgeInsets.only(bottom: index < displayValues.length - 1 ? 8 : 0),
                  child: ThisEmailOutput(
                    id: '${id}_$index',
                    label: '',
                    value: email,
                    showLabel: false,
                    copyable: copyable,
                    clickable: clickable,
                    displayFormat: displayFormat,
                    showIcon: showIcon,
                    padding: padding,
                    backgroundColor: backgroundColor,
                    borderRadius: borderRadius,
                    border: border,
                  ),
                );
              }),
              if (hasMore)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    '+${values.length - maxDisplayItems!} $moreItemsText',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                ),
            ],
          ),
      ],
    );
  }
}
