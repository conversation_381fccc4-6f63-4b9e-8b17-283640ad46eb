import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Widget Test App',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const WidgetTestPage(),
    );
  }
}

class WidgetTestPage extends StatefulWidget {
  const WidgetTestPage({super.key});

  @override
  State<WidgetTestPage> createState() => _WidgetTestPageState();
}

class _WidgetTestPageState extends State<WidgetTestPage> {
  // Form state variables
  String _textValue = '';
  String _textareaValue = '';
  String _emailValue = '';
  String _phoneValue = '';
  String _numberValue = '';
  List<String> _checkboxValues = [];
  String? _radioValue;
  int? _yearValue;
  int? _monthValue;
  int? _dayValue;
  TimeOfDay? _timeValue;

  // Sample data for options
  final List<CheckboxOption> _checkboxOptions = [
    const CheckboxOption(value: 'option1', label: 'Email Notifications'),
    const CheckboxOption(value: 'option2', label: 'SMS Notifications'),
    const CheckboxOption(value: 'option3', label: 'Push Notifications'),
    const CheckboxOption(value: 'option4', label: 'Marketing Updates'),
  ];

  final List<RadioOption> _radioOptions = [
    const RadioOption(value: 'male', label: 'Male', description: 'Male gender'),
    const RadioOption(value: 'female', label: 'Female', description: 'Female gender'),
    const RadioOption(value: 'other', label: 'Other', description: 'Other gender'),
    const RadioOption(value: 'prefer_not_to_say', label: 'Prefer not to say', description: 'Prefer not to disclose'),
  ];

  final List<CheckboxDisplayOption> _checkboxDisplayOptions = [
    const CheckboxDisplayOption(value: 'option1', label: 'Email Notifications'),
    const CheckboxDisplayOption(value: 'option2', label: 'SMS Notifications'),
    const CheckboxDisplayOption(value: 'option3', label: 'Push Notifications'),
    const CheckboxDisplayOption(value: 'option4', label: 'Marketing Updates'),
  ];

  final List<RadioDisplayOption> _radioDisplayOptions = [
    const RadioDisplayOption(value: 'male', label: 'Male', description: 'Male gender'),
    const RadioDisplayOption(value: 'female', label: 'Female', description: 'Female gender'),
    const RadioDisplayOption(value: 'other', label: 'Other', description: 'Other gender'),
    const RadioDisplayOption(value: 'prefer_not_to_say', label: 'Prefer not to say', description: 'Prefer not to disclose'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Widget Component Tests',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Testing All Custom Widgets',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            // Text Input/Output Section
            _buildSection(
              'Text Components',
              [
                ThisTextInput(
                  id: 'text_input_test',
                  label: 'Full Name',
                  value: _textValue,
                  onChanged: (value) => setState(() => _textValue = value),
                  placeholder: 'Enter your full name...',
                  required: true,
                  showCharacterCount: true,
                  maxLength: 100,
                  allowClear: true,
                  helpText: 'Enter your complete name as it appears on official documents',
                ),
                const SizedBox(height: 16),
                ThisTextOutput(
                  id: 'text_output_test',
                  label: 'Display Name',
                  value: _textValue.isEmpty ? 'John Doe (Sample)' : _textValue,
                  copyable: true,
                  prefixIcon: Icon(Icons.person, size: 16, color: ColorPalette.white),
                  helpText: 'This shows the entered name',
                ),
              ],
            ),

            // Textarea Input/Output Section
            _buildSection(
              'Textarea Components',
              [
                ThisTextareaInput(
                  id: 'textarea_input_test',
                  label: 'Bio/Description',
                  value: _textareaValue,
                  onChanged: (value) => setState(() => _textareaValue = value),
                  placeholder: 'Tell us about yourself...',
                  minLines: 3,
                  maxLines: 6,
                  showCharacterCount: true,
                  maxLength: 500,
                  helpText: 'Write a brief description about yourself',
                ),
                const SizedBox(height: 16),
                ThisTextareaOutput(
                  id: 'textarea_output_test',
                  label: 'Bio Display',
                  value: _textareaValue.isEmpty ? 'This is a sample bio text that demonstrates how multi-line content is displayed.\n\nIt can contain multiple paragraphs and will show expand/collapse functionality when the content is long enough to warrant it.' : _textareaValue,
                  copyable: true,
                  showWordCount: true,
                  expandable: true,
                  showExpandButton: true,
                ),
              ],
            ),

            // Email Input/Output Section
            _buildSection(
              'Email Components',
              [
                ThisEmailInput(
                  id: 'email_input_test',
                  label: 'Email Address',
                  value: _emailValue,
                  onChanged: (value) => setState(() => _emailValue = value),
                  placeholder: 'Enter your email address...',
                  required: true,
                  allowedDomains: ['gmail.com', 'yahoo.com', 'outlook.com'],
                  showValidationIcon: true,
                  helpText: 'Enter a valid email address from allowed domains',
                ),
                const SizedBox(height: 16),
                ThisEmailOutput(
                  id: 'email_output_test',
                  label: 'Email Display',
                  value: _emailValue.isEmpty ? '<EMAIL>' : _emailValue,
                  copyable: true,
                  clickable: true,
                  showValidationIcon: true,
                  helpText: 'Click to open email client',
                ),
              ],
            ),

            // Phone Input/Output Section
            _buildSection(
              'Phone Components',
              [
                ThisPhoneInput(
                  id: 'phone_input_test',
                  label: 'Phone Number',
                  value: _phoneValue,
                  onChanged: (value) => setState(() => _phoneValue = value),
                  placeholder: 'Enter your phone number...',
                  required: true,
                  defaultCountry: 'US',
                  showFlag: true,
                  allowExtensions: true,
                  helpText: 'Enter your phone number with country code',
                ),
                const SizedBox(height: 16),
                ThisPhoneOutput(
                  id: 'phone_output_test',
                  label: 'Phone Display',
                  value: _phoneValue.isEmpty ? '+****************' : _phoneValue,
                  copyable: true,
                  clickable: true,
                  showFlag: true,
                  displayFormat: PhoneDisplayFormat.full,
                  helpText: 'Click to call this number',
                ),
              ],
            ),

            // Number Input/Output Section
            _buildSection(
              'Number Components',
              [
                ThisNumberInput(
                  id: 'number_input_test',
                  label: 'Amount',
                  value: _numberValue,
                  onChanged: (value) => setState(() => _numberValue = value),
                  placeholder: 'Enter amount...',
                  required: true,
                  min: 0,
                  max: 10000,
                  decimals: 2,
                  currency: '\$',
                  thousandsSeparator: true,
                  helpText: 'Enter a monetary amount',
                ),
                const SizedBox(height: 16),
                ThisNumberOutput(
                  id: 'number_output_test',
                  label: 'Amount Display',
                  value: _numberValue.isEmpty ? 1234.56 : double.tryParse(_numberValue.replaceAll('\$', '').replaceAll(',', '')),
                  copyable: true,
                  displayFormat: NumberDisplayFormat.currency,
                  currency: '\$',
                  decimals: 2,
                  helpText: 'Formatted currency amount',
                ),
              ],
            ),

            // Checkbox Input/Output Section
            _buildSection(
              'Checkbox Components',
              [
                ThisCheckboxInput(
                  id: 'checkbox_input_test',
                  label: 'Notification Preferences',
                  options: _checkboxOptions,
                  value: _checkboxValues,
                  onChanged: (values) => setState(() => _checkboxValues = values),
                  allowSelectAll: true,
                  required: true,
                  minSelected: 1,
                  helpText: 'Select your preferred notification methods',
                ),
                const SizedBox(height: 16),
                ThisCheckboxOutput(
                  id: 'checkbox_output_test',
                  label: 'Selected Preferences',
                  options: _checkboxDisplayOptions,
                  value: _checkboxValues.isEmpty ? ['option1', 'option3'] : _checkboxValues,
                  displayFormat: DisplayFormat.chips,
                  showCheckmarks: true,
                  helpText: 'Your current notification settings',
                ),
              ],
            ),

            // Radio Input/Output Section
            _buildSection(
              'Radio Components',
              [
                ThisRadioInput(
                  id: 'radio_input_test',
                  label: 'Gender',
                  options: _radioOptions,
                  value: _radioValue,
                  onChanged: (value) => setState(() => _radioValue = value),
                  required: true,
                  helpText: 'Please select your gender',
                ),
                const SizedBox(height: 16),
                ThisRadioOutput(
                  id: 'radio_output_test',
                  label: 'Selected Gender',
                  options: _radioDisplayOptions,
                  value: _radioValue ?? 'female',
                  displayStyle: DisplayStyle.detailed,
                  showDescription: true,
                  helpText: 'Your selected gender preference',
                ),
              ],
            ),

            // Year Input/Output Section
            _buildSection(
              'Year Components',
              [
                ThisYearInput(
                  id: 'year_input_test',
                  label: 'Birth Year',
                  value: _yearValue,
                  onChanged: (value) => setState(() => _yearValue = value),
                  showDropdown: true,
                  minYear: 1950,
                  maxYear: 2024,
                  required: true,
                  helpText: 'Select your birth year',
                ),
                const SizedBox(height: 16),
                ThisYearOutput(
                  id: 'year_output_test',
                  label: 'Birth Year Display',
                  value: _yearValue ?? 1990,
                  showRelativeInfo: true,
                  copyable: true,
                  helpText: 'Your birth year with relative information',
                ),
              ],
            ),

            // Month Input/Output Section
            _buildSection(
              'Month Components',
              [
                ThisMonthInput(
                  id: 'month_input_test',
                  label: 'Birth Month',
                  value: _monthValue,
                  onChanged: (value) => setState(() => _monthValue = value),
                  displayFormat: MonthDisplayFormat.full,
                  required: true,
                  helpText: 'Select your birth month',
                ),
                const SizedBox(height: 16),
                ThisMonthOutput(
                  id: 'month_output_test',
                  label: 'Birth Month Display',
                  value: _monthValue ?? DateTime.now().month,
                  displayFormat: MonthDisplayFormat.full,
                  showMonthInfo: true,
                  copyable: true,
                  helpText: 'Your birth month with relative information',
                ),
              ],
            ),

            // Day Input/Output Section
            _buildSection(
              'Day Components',
              [
                ThisDayInput(
                  id: 'day_input_test',
                  label: 'Birth Day',
                  value: _dayValue,
                  onChanged: (value) => setState(() => _dayValue = value),
                  month: _monthValue,
                  year: _yearValue,
                  showDropdown: true,
                  required: true,
                  helpText: 'Select your birth day',
                ),
                const SizedBox(height: 16),
                ThisDayOutput(
                  id: 'day_output_test',
                  label: 'Birth Day Display',
                  value: _dayValue ?? DateTime.now().day,
                  displayFormat: DayDisplayFormat.ordinal,
                  month: _monthValue ?? DateTime.now().month,
                  year: _yearValue ?? DateTime.now().year,
                  showDayInfo: true,
                  copyable: true,
                  helpText: 'Your birth day with weekday information',
                ),
              ],
            ),

            // Time Input/Output Section
            _buildSection(
              'Time Components',
              [
                ThisTimeInput(
                  id: 'time_input_test',
                  label: 'Preferred Meeting Time',
                  value: _timeValue,
                  onChanged: (value) => setState(() => _timeValue = value),
                  use24HourFormat: false,
                  showTimePicker: true,
                  required: true,
                  helpText: 'Select your preferred meeting time',
                ),
                const SizedBox(height: 16),
                ThisTimeOutput(
                  id: 'time_output_test',
                  label: 'Meeting Time Display',
                  value: _timeValue ?? TimeOfDay.now(),
                  use24HourFormat: false,
                  showRelativeTime: true,
                  copyable: true,
                  helpText: 'Your preferred meeting time with relative information',
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Test Summary Section
            _buildSection(
              'Test Summary',
              [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: ColorPalette.green),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '✅ All Widget Components Loaded Successfully!',
                        style: LexendTextStyles.lexend14Bold.copyWith(
                          color: ColorPalette.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Total Components: 22 widgets (11 input + 11 output)\nCompleted: Text, Textarea, Email, Phone, Number, Checkbox, Radio, Year, Month, Day, Time\nNaming Convention: this_componentName_relatedTo\nAll components are parameter-driven and theme-compliant.',
                        style: LexendTextStyles.lexend12Regular.copyWith(
                          color: ColorPalette.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: LexendTextStyles.lexend16Bold.copyWith(
              color: ColorPalette.green,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: ColorPalette.gray700),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
