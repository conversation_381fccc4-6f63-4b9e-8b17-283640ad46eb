import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/app_theme.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widgets.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Widget Test App',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.darkThemeMode,
      home: const WidgetTestPage(),
    );
  }
}

class WidgetTestPage extends StatefulWidget {
  const WidgetTestPage({super.key});

  @override
  State<WidgetTestPage> createState() => _WidgetTestPageState();
}

class _WidgetTestPageState extends State<WidgetTestPage> {
  // Form state variables
  String _textValue = '';
  String _textareaValue = '';
  String _emailValue = '';
  String _phoneValue = '';
  String _numberValue = '';
  List<String> _checkboxValues = [];
  String? _radioValue;
  int? _yearValue;
  int? _monthValue;
  int? _dayValue;
  TimeOfDay? _timeValue;

  // Sample data for options
  final List<CheckboxOption> _checkboxOptions = [
    const CheckboxOption(value: 'option1', label: 'Email Notifications'),
    const CheckboxOption(value: 'option2', label: 'SMS Notifications'),
    const CheckboxOption(value: 'option3', label: 'Push Notifications'),
    const CheckboxOption(value: 'option4', label: 'Marketing Updates'),
  ];

  final List<RadioOption> _radioOptions = [
    const RadioOption(value: 'male', label: 'Male', description: 'Male gender'),
    const RadioOption(value: 'female', label: 'Female', description: 'Female gender'),
    const RadioOption(value: 'other', label: 'Other', description: 'Other gender'),
    const RadioOption(value: 'prefer_not_to_say', label: 'Prefer not to say', description: 'Prefer not to disclose'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorPalette.primaryDarkColor,
        title: Text(
          'Widget Component Tests',
          style: LexendTextStyles.lexend16ExtraLight.copyWith(
            color: ColorPalette.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Testing All Custom Widgets',
              style: LexendTextStyles.lexend18Bold.copyWith(
                color: ColorPalette.white,
              ),
            ),
            const SizedBox(height: 24),

            // Text Input Section
            _buildSection(
              'Text Input',
              [
                ThisTextInput(
                  id: 'text_input_test',
                  label: 'Full Name',
                  value: _textValue,
                  onChanged: (value) => setState(() => _textValue = value),
                  placeholder: 'Enter your full name...',
                  required: true,
                  showCharacterCount: true,
                  maxLength: 100,
                  allowClear: true,
                  helpText: 'Enter your complete name as it appears on official documents',
                ),
              ],
            ),

            // Textarea Input Section
            _buildSection(
              'Textarea Input',
              [
                ThisTextareaInput(
                  id: 'textarea_input_test',
                  label: 'Bio/Description',
                  value: _textareaValue,
                  onChanged: (value) => setState(() => _textareaValue = value),
                  placeholder: 'Tell us about yourself...',
                  minLines: 3,
                  maxLines: 6,
                  showCharacterCount: true,
                  maxLength: 500,
                  helpText: 'Write a brief description about yourself',
                ),
              ],
            ),

            // Email Input Section
            _buildSection(
              'Email Input',
              [
                ThisEmailInput(
                  id: 'email_input_test',
                  label: 'Email Address',
                  value: _emailValue,
                  onChanged: (value) => setState(() => _emailValue = value),
                  placeholder: 'Enter your email address...',
                  required: true,
                  allowedDomains: ['gmail.com', 'yahoo.com', 'outlook.com'],
                  showValidationIcon: true,
                  helpText: 'Enter a valid email address from allowed domains',
                ),
              ],
            ),

            // Phone Input Section
            _buildSection(
              'Phone Input',
              [
                ThisPhoneInput(
                  id: 'phone_input_test',
                  label: 'Phone Number',
                  value: _phoneValue,
                  onChanged: (value) => setState(() => _phoneValue = value),
                  placeholder: 'Enter your phone number...',
                  required: true,
                  defaultCountry: 'US',
                  showFlag: true,
                  allowExtensions: true,
                  helpText: 'Enter your phone number with country code',
                ),
              ],
            ),

            // Number Input Section
            _buildSection(
              'Number Input',
              [
                ThisNumberInput(
                  id: 'number_input_test',
                  label: 'Amount',
                  value: _numberValue,
                  onChanged: (value) => setState(() => _numberValue = value),
                  placeholder: 'Enter amount...',
                  required: true,
                  min: 0,
                  max: 10000,
                  decimals: 2,
                  currency: '\$',
                  thousandsSeparator: true,
                  helpText: 'Enter a monetary amount',
                ),
              ],
            ),

            // Checkbox Input Section
            _buildSection(
              'Checkbox Input',
              [
                ThisCheckboxInput(
                  id: 'checkbox_input_test',
                  label: 'Notification Preferences',
                  options: _checkboxOptions,
                  value: _checkboxValues,
                  onChanged: (values) => setState(() => _checkboxValues = values),
                  allowSelectAll: true,
                  required: true,
                  minSelected: 1,
                  helpText: 'Select your preferred notification methods',
                ),
              ],
            ),

            // Radio Input Section
            _buildSection(
              'Radio Input',
              [
                ThisRadioInput(
                  id: 'radio_input_test',
                  label: 'Gender',
                  options: _radioOptions,
                  value: _radioValue,
                  onChanged: (value) => setState(() => _radioValue = value),
                  required: true,
                  helpText: 'Please select your gender',
                ),
              ],
            ),

            // Year Input Section
            _buildSection(
              'Year Input',
              [
                ThisYearInput(
                  id: 'year_input_test',
                  label: 'Birth Year',
                  value: _yearValue,
                  onChanged: (value) => setState(() => _yearValue = value),
                  showDropdown: true,
                  minYear: 1950,
                  maxYear: 2024,
                  required: true,
                  helpText: 'Select your birth year',
                ),
              ],
            ),

            // Month Input Section
            _buildSection(
              'Month Input',
              [
                ThisMonthInput(
                  id: 'month_input_test',
                  label: 'Birth Month',
                  value: _monthValue,
                  onChanged: (value) => setState(() => _monthValue = value),
                  displayFormat: MonthDisplayFormat.full,
                  required: true,
                  helpText: 'Select your birth month',
                ),
              ],
            ),

            // Day Input Section
            _buildSection(
              'Day Input',
              [
                ThisDayInput(
                  id: 'day_input_test',
                  label: 'Birth Day',
                  value: _dayValue,
                  onChanged: (value) => setState(() => _dayValue = value),
                  month: _monthValue,
                  year: _yearValue,
                  showDropdown: true,
                  required: true,
                  helpText: 'Select your birth day',
                ),
              ],
            ),

            // Time Input Section
            _buildSection(
              'Time Input',
              [
                ThisTimeInput(
                  id: 'time_input_test',
                  label: 'Preferred Meeting Time',
                  value: _timeValue,
                  onChanged: (value) => setState(() => _timeValue = value),
                  use24HourFormat: false,
                  showTimePicker: true,
                  required: true,
                  helpText: 'Select your preferred meeting time',
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Test Summary Section
            _buildSection(
              'Test Summary',
              [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorPalette.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: ColorPalette.green),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '✅ All Input Widget Components Loaded Successfully!',
                        style: LexendTextStyles.lexend14Bold.copyWith(
                          color: ColorPalette.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Total Components: 11 input widgets\nCompleted: Text, Textarea, Email, Phone, Number, Checkbox, Radio, Year, Month, Day, Time\nNaming Convention: this_componentName_input\nAll components are parameter-driven and theme-compliant.',
                        style: LexendTextStyles.lexend12Regular.copyWith(
                          color: ColorPalette.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: LexendTextStyles.lexend16Bold.copyWith(
              color: ColorPalette.green,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ColorPalette.darkToneInk.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: ColorPalette.gray700),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
