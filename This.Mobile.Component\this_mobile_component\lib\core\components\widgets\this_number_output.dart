import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:intl/intl.dart';

/// Display format for number output widgets
enum NumberDisplayFormat {
  raw,         // 1234.56
  formatted,   // 1,234.56
  currency,    // $1,234.56
  percentage,  // 123.45%
  scientific,  // 1.23e+3
  compact,     // 1.2K
}

/// A customizable number output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays numeric data with various formatting options
class ThisNumberOutput extends StatelessWidget {
  final String id;
  final String label;
  final double? value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool selectable;
  final String? helpText;
  final NumberDisplayFormat displayFormat;
  final int? decimals;
  final String? currency;
  final String? locale;
  final bool showIcon;
  final bool showValidationIcon;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool validateNumber;

  const ThisNumberOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.selectable = true,
    this.helpText,
    this.displayFormat = NumberDisplayFormat.formatted,
    this.decimals,
    this.currency,
    this.locale,
    this.showIcon = true,
    this.showValidationIcon = false,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.validateNumber = true,
  });

  bool _isValidNumber(double? number) {
    return number != null && !number.isNaN && number.isFinite;
  }

  String _formatNumber(double number) {
    if (!_isValidNumber(number)) return '';

    switch (displayFormat) {
      case NumberDisplayFormat.raw:
        return number.toString();
      
      case NumberDisplayFormat.formatted:
        final formatter = NumberFormat('#,##0${decimals != null ? '.${'0' * decimals!}' : '.##'}', locale);
        return formatter.format(number);
      
      case NumberDisplayFormat.currency:
        final currencySymbol = currency ?? '\$';
        final formatter = NumberFormat.currency(
          symbol: currencySymbol,
          decimalDigits: decimals ?? 2,
          locale: locale,
        );
        return formatter.format(number);
      
      case NumberDisplayFormat.percentage:
        final percentage = number * 100;
        final formatter = NumberFormat('#,##0${decimals != null ? '.${'0' * decimals!}' : '.##'}%', locale);
        return formatter.format(percentage);
      
      case NumberDisplayFormat.scientific:
        return number.toStringAsExponential(decimals ?? 2);
      
      case NumberDisplayFormat.compact:
        final formatter = NumberFormat.compact(locale: locale);
        return formatter.format(number);
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (value != null) {
      final textToCopy = _formatNumber(value!);
      Clipboard.setData(ClipboardData(text: textToCopy));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Number copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  Widget? _getValidationIcon() {
    if (!showValidationIcon || value == null) return null;

    final isValid = validateNumber ? _isValidNumber(value) : true;
    return Icon(
      isValid ? Icons.check_circle : Icons.error,
      size: 16,
      color: isValid ? ColorPalette.green : const Color(0xFFC73E1D),
    );
  }

  Color _getNumberColor() {
    if (value == null) return ColorPalette.placeHolderTextColor;
    if (!validateNumber) return ColorPalette.white;
    
    if (!_isValidNumber(value)) return const Color(0xFFC73E1D);
    
    // Color coding for positive/negative numbers
    if (value! > 0) return ColorPalette.green;
    if (value! < 0) return const Color(0xFFC73E1D);
    return ColorPalette.white; // Zero
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value != null ? _formatNumber(value!) : (placeholder ?? '');
    final isEmpty = value == null;
    final isValid = validateNumber ? _isValidNumber(value) : true;
    final numberColor = _getNumberColor();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ?? LexendTextStyles.lexend14Medium.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        
        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withValues(alpha: 0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ?? Border.all(
                color: validateNumber && !isEmpty
                    ? (isValid ? ColorPalette.green.withValues(alpha: 0.3) : const Color(0xFFC73E1D).withValues(alpha: 0.3))
                    : ColorPalette.gray300.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Prefix Icon or Number Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ] else if (showIcon) ...[
                  Icon(
                    Icons.numbers,
                    size: 16,
                    color: isEmpty 
                        ? ColorPalette.placeHolderTextColor 
                        : numberColor,
                  ),
                  const SizedBox(width: 8),
                ],
                
                // Number Content
                Expanded(
                  child: selectable
                      ? SelectableText(
                          displayValue,
                          style: customTextStyle ?? LexendTextStyles.lexend14Regular.copyWith(
                            color: isEmpty 
                                ? ColorPalette.placeHolderTextColor 
                                : numberColor,
                            fontWeight: isEmpty ? FontWeight.normal : FontWeight.w600,
                          ),
                        )
                      : Text(
                          displayValue,
                          style: customTextStyle ?? LexendTextStyles.lexend14Regular.copyWith(
                            color: isEmpty 
                                ? ColorPalette.placeHolderTextColor 
                                : numberColor,
                            fontWeight: isEmpty ? FontWeight.normal : FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                ),
                
                // Validation Icon
                if (_getValidationIcon() != null) ...[
                  const SizedBox(width: 8),
                  _getValidationIcon()!,
                ],
                
                // Copy Button
                if (copyable && value != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy number to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],
                
                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A specialized version of ThisNumberOutput for displaying number ranges
class ThisNumberRangeOutput extends StatelessWidget {
  final String id;
  final String label;
  final double? minValue;
  final double? maxValue;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final String separator;
  final NumberDisplayFormat displayFormat;
  final int? decimals;
  final String? currency;
  final String? locale;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;

  const ThisNumberRangeOutput({
    super.key,
    required this.id,
    required this.label,
    required this.minValue,
    required this.maxValue,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.separator = ' - ',
    this.displayFormat = NumberDisplayFormat.formatted,
    this.decimals,
    this.currency,
    this.locale,
    this.customTextStyle,
    this.customLabelStyle,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
  });

  String _formatNumber(double number) {
    switch (displayFormat) {
      case NumberDisplayFormat.raw:
        return number.toString();
      case NumberDisplayFormat.formatted:
        final formatter = NumberFormat('#,##0${decimals != null ? '.${'0' * decimals!}' : '.##'}', locale);
        return formatter.format(number);
      case NumberDisplayFormat.currency:
        final currencySymbol = currency ?? '\$';
        final formatter = NumberFormat.currency(
          symbol: currencySymbol,
          decimalDigits: decimals ?? 2,
          locale: locale,
        );
        return formatter.format(number);
      case NumberDisplayFormat.percentage:
        final percentage = number * 100;
        final formatter = NumberFormat('#,##0${decimals != null ? '.${'0' * decimals!}' : '.##'}%', locale);
        return formatter.format(percentage);
      case NumberDisplayFormat.scientific:
        return number.toStringAsExponential(decimals ?? 2);
      case NumberDisplayFormat.compact:
        final formatter = NumberFormat.compact(locale: locale);
        return formatter.format(number);
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (minValue != null && maxValue != null) {
      final text = '${_formatNumber(minValue!)}$separator${_formatNumber(maxValue!)}';
      Clipboard.setData(ClipboardData(text: text));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Number range copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String displayValue;
    bool isEmpty;

    if (minValue != null && maxValue != null) {
      displayValue = '${_formatNumber(minValue!)}$separator${_formatNumber(maxValue!)}';
      isEmpty = false;
    } else if (minValue != null) {
      displayValue = '${_formatNumber(minValue!)}$separator?';
      isEmpty = false;
    } else if (maxValue != null) {
      displayValue = '?$separator${_formatNumber(maxValue!)}';
      isEmpty = false;
    } else {
      displayValue = placeholder ?? '';
      isEmpty = true;
    }

    return ThisNumberOutput(
      id: id,
      label: label,
      value: null, // We handle display manually
      placeholder: displayValue,
      showLabel: showLabel,
      copyable: false, // We handle copy manually
      helpText: helpText,
      displayFormat: displayFormat,
      decimals: decimals,
      currency: currency,
      locale: locale,
      customTextStyle: customTextStyle,
      customLabelStyle: customLabelStyle,
      padding: padding,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      border: border,
      onTap: onTap,
      suffixIcon: copyable && !isEmpty
          ? IconButton(
              icon: const Icon(Icons.copy, size: 16),
              onPressed: () => _copyToClipboard(context),
              color: ColorPalette.placeHolderTextColor,
              tooltip: 'Copy number range to clipboard',
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: const EdgeInsets.all(4),
            )
          : null,
    );
  }
}
