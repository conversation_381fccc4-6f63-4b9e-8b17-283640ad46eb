import 'package:this_mobile_component/core/components/widgets/this_dropdown_input.dart';

/// API Component Model based on the provided API response structure
/// This model represents the complete component configuration from the API
class ApiComponentModel {
  final String id;
  final String name;
  final String displayName;
  final String category;
  final String uiComponent;
  final String? validationPattern;
  final int? minLength;
  final int? maxLength;
  final double? minValue;
  final double? maxValue;
  final int? decimalPlaces;
  final double? stepValue;
  final bool isRequired;
  final String inputType;
  final String? inputMask;
  final String? placeholder;
  final Map<String, dynamic>? htmlAttributes;
  final List<String>? defaultOptions;
  final bool allowsMultiple;
  final bool allowsCustomOptions;
  final int? maxSelections;
  final List<String>? allowedFileTypes;
  final int? maxFileSizeBytes;
  final String requiredErrorMessage;
  final String? patternErrorMessage;
  final String? minLengthErrorMessage;
  final String? maxLengthErrorMessage;
  final String? minValueErrorMessage;
  final String? maxValueErrorMessage;
  final String? fileTypeErrorMessage;
  final String? fileSizeErrorMessage;
  final bool isActive;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? modifiedAt;
  final String? modifiedBy;

  const ApiComponentModel({
    required this.id,
    required this.name,
    required this.displayName,
    required this.category,
    required this.uiComponent,
    this.validationPattern,
    this.minLength,
    this.maxLength,
    this.minValue,
    this.maxValue,
    this.decimalPlaces,
    this.stepValue,
    required this.isRequired,
    required this.inputType,
    this.inputMask,
    this.placeholder,
    this.htmlAttributes,
    this.defaultOptions,
    required this.allowsMultiple,
    required this.allowsCustomOptions,
    this.maxSelections,
    this.allowedFileTypes,
    this.maxFileSizeBytes,
    required this.requiredErrorMessage,
    this.patternErrorMessage,
    this.minLengthErrorMessage,
    this.maxLengthErrorMessage,
    this.minValueErrorMessage,
    this.maxValueErrorMessage,
    this.fileTypeErrorMessage,
    this.fileSizeErrorMessage,
    required this.isActive,
    required this.createdAt,
    required this.createdBy,
    this.modifiedAt,
    this.modifiedBy,
  });

  /// Factory constructor to create ApiComponentModel from JSON (API response)
  factory ApiComponentModel.fromJson(Map<String, dynamic> json) {
    return ApiComponentModel(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      category: json['category'] as String,
      uiComponent: json['uiComponent'] as String,
      validationPattern: json['validationPattern'] as String?,
      minLength: json['minLength'] as int?,
      maxLength: json['maxLength'] as int?,
      minValue: json['minValue']?.toDouble(),
      maxValue: json['maxValue']?.toDouble(),
      decimalPlaces: json['decimalPlaces'] as int?,
      stepValue: json['stepValue']?.toDouble(),
      isRequired: json['isRequired'] as bool,
      inputType: json['inputType'] as String,
      inputMask: json['inputMask'] as String?,
      placeholder: json['placeholder'] as String?,
      htmlAttributes: json['htmlAttributes'] as Map<String, dynamic>?,
      defaultOptions: json['defaultOptions'] != null 
          ? List<String>.from(json['defaultOptions']) 
          : null,
      allowsMultiple: json['allowsMultiple'] as bool,
      allowsCustomOptions: json['allowsCustomOptions'] as bool,
      maxSelections: json['maxSelections'] as int?,
      allowedFileTypes: json['allowedFileTypes'] != null 
          ? List<String>.from(json['allowedFileTypes'].toString().split(','))
          : null,
      maxFileSizeBytes: json['maxFileSizeBytes'] as int?,
      requiredErrorMessage: json['requiredErrorMessage'] as String,
      patternErrorMessage: json['patternErrorMessage'] as String?,
      minLengthErrorMessage: json['minLengthErrorMessage'] as String?,
      maxLengthErrorMessage: json['maxLengthErrorMessage'] as String?,
      minValueErrorMessage: json['minValueErrorMessage'] as String?,
      maxValueErrorMessage: json['maxValueErrorMessage'] as String?,
      fileTypeErrorMessage: json['fileTypeErrorMessage'] as String?,
      fileSizeErrorMessage: json['fileSizeErrorMessage'] as String?,
      isActive: json['isActive'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      createdBy: json['createdBy'] as String,
      modifiedAt: json['modifiedAt'] != null 
          ? DateTime.parse(json['modifiedAt'] as String) 
          : null,
      modifiedBy: json['modifiedBy'] as String?,
    );
  }

  /// Convert to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'category': category,
      'uiComponent': uiComponent,
      'validationPattern': validationPattern,
      'minLength': minLength,
      'maxLength': maxLength,
      'minValue': minValue,
      'maxValue': maxValue,
      'decimalPlaces': decimalPlaces,
      'stepValue': stepValue,
      'isRequired': isRequired,
      'inputType': inputType,
      'inputMask': inputMask,
      'placeholder': placeholder,
      'htmlAttributes': htmlAttributes,
      'defaultOptions': defaultOptions,
      'allowsMultiple': allowsMultiple,
      'allowsCustomOptions': allowsCustomOptions,
      'maxSelections': maxSelections,
      'allowedFileTypes': allowedFileTypes?.join(','),
      'maxFileSizeBytes': maxFileSizeBytes,
      'requiredErrorMessage': requiredErrorMessage,
      'patternErrorMessage': patternErrorMessage,
      'minLengthErrorMessage': minLengthErrorMessage,
      'maxLengthErrorMessage': maxLengthErrorMessage,
      'minValueErrorMessage': minValueErrorMessage,
      'maxValueErrorMessage': maxValueErrorMessage,
      'fileTypeErrorMessage': fileTypeErrorMessage,
      'fileSizeErrorMessage': fileSizeErrorMessage,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'modifiedAt': modifiedAt?.toIso8601String(),
      'modifiedBy': modifiedBy,
    };
  }

  /// Get the appropriate widget type based on uiComponent
  WidgetType get widgetType {
    switch (uiComponent.toLowerCase()) {
      case 'textinput':
      case 'addressinput':
        return WidgetType.text;
      case 'emailinput':
        return WidgetType.email;
      case 'phoneinput':
        return WidgetType.phone;
      case 'numberinput':
        return WidgetType.number;
      case 'currencyinput':
        return WidgetType.currency;
      case 'percentageinput':
        return WidgetType.percentage;
      case 'checkbox':
        return WidgetType.checkbox;
      case 'radio':
        return WidgetType.radio;
      case 'dropdown':
      case 'select':
        return WidgetType.dropdown;
      case 'slider':
      case 'range':
        return WidgetType.slider;
      case 'fileupload':
        return WidgetType.file;
      case 'imageupload':
        return WidgetType.image;
      case 'videoupload':
        return WidgetType.video;
      case 'datepicker':
        return WidgetType.date;
      case 'datetimepicker':
        return WidgetType.datetime;
      case 'timepicker':
        return WidgetType.time;
      case 'colorpicker':
        return WidgetType.color;
      case 'textarea':
        return WidgetType.textarea;
      case 'yearpicker':
        return WidgetType.year;
      case 'monthpicker':
        return WidgetType.month;
      case 'daypicker':
        return WidgetType.day;
      default:
        return WidgetType.text; // Default fallback
    }
  }

  /// Check if component should be rendered (is active)
  bool get shouldRender => isActive;

  /// Get display label (use displayName if available, otherwise name)
  String get label => displayName.isNotEmpty ? displayName : name;

  /// Get default options as DropdownOption list
  List<DropdownOption> get dropdownOptions {
    if (defaultOptions == null || defaultOptions!.isEmpty) {
      return [];
    }
    
    return defaultOptions!.map((option) => DropdownOption(
      value: option,
      label: option,
    )).toList();
  }

  /// Copy with method for updating specific fields
  ApiComponentModel copyWith({
    String? id,
    String? name,
    String? displayName,
    String? category,
    String? uiComponent,
    String? validationPattern,
    int? minLength,
    int? maxLength,
    double? minValue,
    double? maxValue,
    int? decimalPlaces,
    double? stepValue,
    bool? isRequired,
    String? inputType,
    String? inputMask,
    String? placeholder,
    Map<String, dynamic>? htmlAttributes,
    List<String>? defaultOptions,
    bool? allowsMultiple,
    bool? allowsCustomOptions,
    int? maxSelections,
    List<String>? allowedFileTypes,
    int? maxFileSizeBytes,
    String? requiredErrorMessage,
    String? patternErrorMessage,
    String? minLengthErrorMessage,
    String? maxLengthErrorMessage,
    String? minValueErrorMessage,
    String? maxValueErrorMessage,
    String? fileTypeErrorMessage,
    String? fileSizeErrorMessage,
    bool? isActive,
    DateTime? createdAt,
    String? createdBy,
    DateTime? modifiedAt,
    String? modifiedBy,
  }) {
    return ApiComponentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      category: category ?? this.category,
      uiComponent: uiComponent ?? this.uiComponent,
      validationPattern: validationPattern ?? this.validationPattern,
      minLength: minLength ?? this.minLength,
      maxLength: maxLength ?? this.maxLength,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      decimalPlaces: decimalPlaces ?? this.decimalPlaces,
      stepValue: stepValue ?? this.stepValue,
      isRequired: isRequired ?? this.isRequired,
      inputType: inputType ?? this.inputType,
      inputMask: inputMask ?? this.inputMask,
      placeholder: placeholder ?? this.placeholder,
      htmlAttributes: htmlAttributes ?? this.htmlAttributes,
      defaultOptions: defaultOptions ?? this.defaultOptions,
      allowsMultiple: allowsMultiple ?? this.allowsMultiple,
      allowsCustomOptions: allowsCustomOptions ?? this.allowsCustomOptions,
      maxSelections: maxSelections ?? this.maxSelections,
      allowedFileTypes: allowedFileTypes ?? this.allowedFileTypes,
      maxFileSizeBytes: maxFileSizeBytes ?? this.maxFileSizeBytes,
      requiredErrorMessage: requiredErrorMessage ?? this.requiredErrorMessage,
      patternErrorMessage: patternErrorMessage ?? this.patternErrorMessage,
      minLengthErrorMessage: minLengthErrorMessage ?? this.minLengthErrorMessage,
      maxLengthErrorMessage: maxLengthErrorMessage ?? this.maxLengthErrorMessage,
      minValueErrorMessage: minValueErrorMessage ?? this.minValueErrorMessage,
      maxValueErrorMessage: maxValueErrorMessage ?? this.maxValueErrorMessage,
      fileTypeErrorMessage: fileTypeErrorMessage ?? this.fileTypeErrorMessage,
      fileSizeErrorMessage: fileSizeErrorMessage ?? this.fileSizeErrorMessage,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      modifiedBy: modifiedBy ?? this.modifiedBy,
    );
  }
}

/// Widget types enum for component mapping
enum WidgetType {
  text,
  textarea,
  email,
  phone,
  number,
  currency,
  percentage,
  checkbox,
  radio,
  dropdown,
  slider,
  file,
  image,
  video,
  date,
  datetime,
  time,
  color,
  year,
  month,
  day,
}

/// API Response model for paginated component list
class ApiComponentResponse {
  final bool hasPreviousPage;
  final bool hasNextPage;
  final bool succeeded;
  final List<ApiComponentModel> data;

  const ApiComponentResponse({
    required this.hasPreviousPage,
    required this.hasNextPage,
    required this.succeeded,
    required this.data,
  });

  factory ApiComponentResponse.fromJson(Map<String, dynamic> json) {
    return ApiComponentResponse(
      hasPreviousPage: json['hasPreviousPage'] as bool,
      hasNextPage: json['hasNextPage'] as bool,
      succeeded: json['succeeded'] as bool,
      data: (json['data'] as List)
          .map((item) => ApiComponentModel.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hasPreviousPage': hasPreviousPage,
      'hasNextPage': hasNextPage,
      'succeeded': succeeded,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}
