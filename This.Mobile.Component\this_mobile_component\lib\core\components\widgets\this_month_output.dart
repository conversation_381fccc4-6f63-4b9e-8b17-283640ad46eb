import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Month option model for display
class MonthDisplayOption {
  final int value;
  final String name;
  final String shortName;

  const MonthDisplayOption({
    required this.value,
    required this.name,
    required this.shortName,
  });
}

/// A customizable month output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays month data with various formatting options
class ThisMonthOutput extends StatelessWidget {
  final String id;
  final String label;
  final int? value; // 1-12
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final MonthDisplayFormat displayFormat;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showMonthInfo;

  const ThisMonthOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.displayFormat = MonthDisplayFormat.full,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showMonthInfo = false,
  });

  static const List<MonthDisplayOption> _allMonths = [
    MonthDisplayOption(value: 1, name: 'January', shortName: 'Jan'),
    MonthDisplayOption(value: 2, name: 'February', shortName: 'Feb'),
    MonthDisplayOption(value: 3, name: 'March', shortName: 'Mar'),
    MonthDisplayOption(value: 4, name: 'April', shortName: 'Apr'),
    MonthDisplayOption(value: 5, name: 'May', shortName: 'May'),
    MonthDisplayOption(value: 6, name: 'June', shortName: 'Jun'),
    MonthDisplayOption(value: 7, name: 'July', shortName: 'Jul'),
    MonthDisplayOption(value: 8, name: 'August', shortName: 'Aug'),
    MonthDisplayOption(value: 9, name: 'September', shortName: 'Sep'),
    MonthDisplayOption(value: 10, name: 'October', shortName: 'Oct'),
    MonthDisplayOption(value: 11, name: 'November', shortName: 'Nov'),
    MonthDisplayOption(value: 12, name: 'December', shortName: 'Dec'),
  ];

  String _formatMonth(int monthValue) {
    final month = _allMonths.firstWhere((m) => m.value == monthValue);
    switch (displayFormat) {
      case MonthDisplayFormat.full:
        return month.name;
      case MonthDisplayFormat.short:
        return month.shortName;
      case MonthDisplayFormat.number:
        return monthValue.toString().padLeft(2, '0');
    }
  }

  String _getMonthInfo(int monthValue) {
    final currentMonth = DateTime.now().month;
    final difference = monthValue - currentMonth;

    if (difference == 0) {
      return 'Current month';
    } else if (difference > 0) {
      return '$difference month${difference > 1 ? 's' : ''} from now';
    } else {
      return '${difference.abs()} month${difference.abs() > 1 ? 's' : ''} ago';
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (value != null) {
      Clipboard.setData(ClipboardData(text: _formatMonth(value!)));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value != null ? _formatMonth(value!) : (placeholder ?? '');
    final isEmpty = value == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: Row(
              children: [
                // Prefix Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],

                // Month Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayValue,
                        style: customTextStyle ??
                            LexendTextStyles.lexend14Regular.copyWith(
                              color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                            ),
                      ),
                      if (showMonthInfo && value != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: Text(
                            _getMonthInfo(value!),
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.placeHolderTextColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Copy Button
                if (copyable && value != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],

                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

enum MonthDisplayFormat {
  full, // January, February, etc.
  short, // Jan, Feb, etc.
  number, // 01, 02, etc.
}
