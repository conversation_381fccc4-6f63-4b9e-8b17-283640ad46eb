import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Option model for checkbox display items
class CheckboxDisplayOption {
  final String value;
  final String label;
  final Widget? icon;
  final Color? color;

  const CheckboxDisplayOption({
    required this.value,
    required this.label,
    this.icon,
    this.color,
  });
}

/// A customizable checkbox output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays selected checkbox values in various formats
class ThisCheckboxOutput extends StatelessWidget {
  final String id;
  final String label;
  final List<CheckboxDisplayOption> options;
  final List<String> value;
  final String? placeholder;
  final bool showLabel;
  final String? helpText;
  final DisplayFormat displayFormat;
  final String separator;
  final int? maxDisplayItems;
  final String moreItemsText;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showIcons;
  final bool showCheckmarks;

  const ThisCheckboxOutput({
    super.key,
    required this.id,
    required this.label,
    required this.options,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.helpText,
    this.displayFormat = DisplayFormat.list,
    this.separator = ', ',
    this.maxDisplayItems,
    this.moreItemsText = 'more',
    this.customTextStyle,
    this.customLabelStyle,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showIcons = true,
    this.showCheckmarks = true,
  });

  List<CheckboxDisplayOption> get _selectedOptions {
    return options.where((option) => value.contains(option.value)).toList();
  }

  @override
  Widget build(BuildContext context) {
    final selectedOptions = _selectedOptions;
    final isEmpty = selectedOptions.isEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: isEmpty
                ? Text(
                    placeholder ?? 'No items selected',
                    style: customTextStyle ??
                        LexendTextStyles.lexend14Regular.copyWith(
                          color: ColorPalette.placeHolderTextColor,
                        ),
                  )
                : _buildContent(selectedOptions),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(List<CheckboxDisplayOption> selectedOptions) {
    switch (displayFormat) {
      case DisplayFormat.list:
        return _buildListFormat(selectedOptions);
      case DisplayFormat.chips:
        return _buildChipsFormat(selectedOptions);
      case DisplayFormat.badges:
        return _buildBadgesFormat(selectedOptions);
      case DisplayFormat.inline:
        return _buildInlineFormat(selectedOptions);
    }
  }

  Widget _buildListFormat(List<CheckboxDisplayOption> selectedOptions) {
    final displayOptions = maxDisplayItems != null && selectedOptions.length > maxDisplayItems! ? selectedOptions.take(maxDisplayItems!).toList() : selectedOptions;

    final hasMore = maxDisplayItems != null && selectedOptions.length > maxDisplayItems!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...displayOptions.map((option) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  if (showCheckmarks)
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: option.color ?? ColorPalette.white,
                    ),
                  if (showCheckmarks) const SizedBox(width: 8),
                  if (showIcons && option.icon != null) ...[
                    option.icon!,
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: Text(
                      option.label,
                      style: customTextStyle ??
                          LexendTextStyles.lexend14Regular.copyWith(
                            color: ColorPalette.white,
                          ),
                    ),
                  ),
                ],
              ),
            )),
        if (hasMore)
          Text(
            '+${selectedOptions.length - maxDisplayItems!} $moreItemsText',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.placeHolderTextColor,
            ),
          ),
      ],
    );
  }

  Widget _buildChipsFormat(List<CheckboxDisplayOption> selectedOptions) {
    final displayOptions = maxDisplayItems != null && selectedOptions.length > maxDisplayItems! ? selectedOptions.take(maxDisplayItems!).toList() : selectedOptions;

    final hasMore = maxDisplayItems != null && selectedOptions.length > maxDisplayItems!;

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        ...displayOptions.map((option) => Chip(
              avatar: showIcons && option.icon != null ? option.icon : null,
              label: Text(
                option.label,
                style: LexendTextStyles.lexend12Regular.copyWith(
                  color: ColorPalette.primaryDarkColor,
                ),
              ),
              backgroundColor: option.color ?? ColorPalette.white,
              side: BorderSide.none,
            )),
        if (hasMore)
          Chip(
            label: Text(
              '+${selectedOptions.length - maxDisplayItems!}',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.primaryDarkColor,
              ),
            ),
            backgroundColor: ColorPalette.placeHolderTextColor,
            side: BorderSide.none,
          ),
      ],
    );
  }

  Widget _buildBadgesFormat(List<CheckboxDisplayOption> selectedOptions) {
    final displayOptions = maxDisplayItems != null && selectedOptions.length > maxDisplayItems! ? selectedOptions.take(maxDisplayItems!).toList() : selectedOptions;

    final hasMore = maxDisplayItems != null && selectedOptions.length > maxDisplayItems!;

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        ...displayOptions.map((option) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: (option.color ?? ColorPalette.white).withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: option.color ?? ColorPalette.white,
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (showIcons && option.icon != null) ...[
                    option.icon!,
                    const SizedBox(width: 4),
                  ],
                  Text(
                    option.label,
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: option.color ?? ColorPalette.white,
                    ),
                  ),
                ],
              ),
            )),
        if (hasMore)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: ColorPalette.placeHolderTextColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: ColorPalette.placeHolderTextColor,
                width: 1,
              ),
            ),
            child: Text(
              '+${selectedOptions.length - maxDisplayItems!}',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInlineFormat(List<CheckboxDisplayOption> selectedOptions) {
    final displayOptions = maxDisplayItems != null && selectedOptions.length > maxDisplayItems! ? selectedOptions.take(maxDisplayItems!).toList() : selectedOptions;

    final hasMore = maxDisplayItems != null && selectedOptions.length > maxDisplayItems!;

    final text = displayOptions.map((option) => option.label).join(separator);
    final moreText = hasMore ? '$separator+${selectedOptions.length - maxDisplayItems!} $moreItemsText' : '';

    return Text(
      '$text$moreText',
      style: customTextStyle ??
          LexendTextStyles.lexend14Regular.copyWith(
            color: ColorPalette.white,
          ),
    );
  }
}

enum DisplayFormat {
  list,
  chips,
  badges,
  inline,
}
