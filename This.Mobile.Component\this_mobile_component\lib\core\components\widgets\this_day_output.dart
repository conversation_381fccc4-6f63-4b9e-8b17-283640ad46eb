import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/widget_enums.dart';

/// A customizable day output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays day data with various formatting options
class ThisDayOutput extends StatelessWidget {
  final String id;
  final String label;
  final int? value; // 1-31
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final DayDisplayFormat displayFormat;
  final int? month; // 1-12, used for context
  final int? year; // Used for context
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showDayInfo;

  const ThisDayOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.displayFormat = DayDisplayFormat.number,
    this.month,
    this.year,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showDayInfo = false,
  });

  String _formatDay(int dayValue) {
    switch (displayFormat) {
      case DayDisplayFormat.number:
        return dayValue.toString();
      case DayDisplayFormat.paddedNumber:
        return dayValue.toString().padLeft(2, '0');
      case DayDisplayFormat.ordinal:
        return _getOrdinalNumber(dayValue);
    }
  }

  String _getOrdinalNumber(int number) {
    if (number >= 11 && number <= 13) {
      return '${number}th';
    }
    switch (number % 10) {
      case 1:
        return '${number}st';
      case 2:
        return '${number}nd';
      case 3:
        return '${number}rd';
      default:
        return '${number}th';
    }
  }

  String _getDayInfo(int dayValue) {
    if (month != null && year != null) {
      final date = DateTime(year!, month!, dayValue);
      final weekday = _getWeekdayName(date.weekday);
      return weekday;
    }
    return '';
  }

  String _getWeekdayName(int weekday) {
    const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return weekdays[weekday - 1];
  }

  void _copyToClipboard(BuildContext context) {
    if (value != null) {
      Clipboard.setData(ClipboardData(text: _formatDay(value!)));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value != null ? _formatDay(value!) : (placeholder ?? '');
    final isEmpty = value == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: Row(
              children: [
                // Prefix Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ],

                // Day Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayValue,
                        style: customTextStyle ??
                            LexendTextStyles.lexend14Regular.copyWith(
                              color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                            ),
                      ),
                      if (showDayInfo && value != null && month != null && year != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: Text(
                            _getDayInfo(value!),
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.placeHolderTextColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Copy Button
                if (copyable && value != null) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],

                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// A specialized version of ThisDayOutput for displaying day ranges
class ThisDayRangeOutput extends StatelessWidget {
  final String id;
  final String label;
  final int? startDay;
  final int? endDay;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final String? helpText;
  final String separator;
  final DayDisplayFormat displayFormat;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;

  const ThisDayRangeOutput({
    super.key,
    required this.id,
    required this.label,
    required this.startDay,
    required this.endDay,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.helpText,
    this.separator = ' - ',
    this.displayFormat = DayDisplayFormat.number,
    this.customTextStyle,
    this.customLabelStyle,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
  });

  String _formatDay(int dayValue) {
    switch (displayFormat) {
      case DayDisplayFormat.number:
        return dayValue.toString();
      case DayDisplayFormat.paddedNumber:
        return dayValue.toString().padLeft(2, '0');
      case DayDisplayFormat.ordinal:
        return _getOrdinalNumber(dayValue);
    }
  }

  String _getOrdinalNumber(int number) {
    if (number >= 11 && number <= 13) {
      return '${number}th';
    }
    switch (number % 10) {
      case 1:
        return '${number}st';
      case 2:
        return '${number}nd';
      case 3:
        return '${number}rd';
      default:
        return '${number}th';
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (startDay != null && endDay != null) {
      final text = '${_formatDay(startDay!)}$separator${_formatDay(endDay!)}';
      Clipboard.setData(ClipboardData(text: text));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    String displayValue;
    bool isEmpty;

    if (startDay != null && endDay != null) {
      displayValue = '${_formatDay(startDay!)}$separator${_formatDay(endDay!)}';
      isEmpty = false;
    } else if (startDay != null) {
      displayValue = '${_formatDay(startDay!)}$separator?';
      isEmpty = false;
    } else if (endDay != null) {
      displayValue = '?$separator${_formatDay(endDay!)}';
      isEmpty = false;
    } else {
      displayValue = placeholder ?? '';
      isEmpty = true;
    }

    return ThisDayOutput(
      id: id,
      label: label,
      value: null, // We handle display manually
      placeholder: displayValue,
      showLabel: showLabel,
      copyable: false, // We handle copy manually
      helpText: helpText,
      displayFormat: displayFormat,
      customTextStyle: customTextStyle,
      customLabelStyle: customLabelStyle,
      padding: padding,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      border: border,
      onTap: onTap,
      suffixIcon: copyable && !isEmpty
          ? IconButton(
              icon: const Icon(Icons.copy, size: 16),
              onPressed: () => _copyToClipboard(context),
              color: ColorPalette.placeHolderTextColor,
              tooltip: 'Copy to clipboard',
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              padding: const EdgeInsets.all(4),
            )
          : null,
    );
  }
}
