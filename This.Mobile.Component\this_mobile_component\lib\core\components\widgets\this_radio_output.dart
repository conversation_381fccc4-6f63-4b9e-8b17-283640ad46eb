import 'package:flutter/material.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// Option model for radio display items
class RadioDisplayOption {
  final String value;
  final String label;
  final String? description;
  final Widget? icon;
  final Color? color;

  const RadioDisplayOption({
    required this.value,
    required this.label,
    this.description,
    this.icon,
    this.color,
  });
}

/// A customizable radio output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays the selected radio value in various formats
class ThisRadioOutput extends StatelessWidget {
  final String id;
  final String label;
  final List<RadioDisplayOption> options;
  final String? value;
  final String? placeholder;
  final bool showLabel;
  final String? helpText;
  final DisplayStyle displayStyle;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showIcon;
  final bool showDescription;

  const ThisRadioOutput({
    super.key,
    required this.id,
    required this.label,
    required this.options,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.helpText,
    this.displayStyle = DisplayStyle.simple,
    this.customTextStyle,
    this.customLabelStyle,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showIcon = true,
    this.showDescription = false,
  });

  RadioDisplayOption? get _selectedOption {
    if (value == null) return null;
    try {
      return options.firstWhere((option) => option.value == value);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedOption = _selectedOption;
    final isEmpty = selectedOption == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withOpacity(0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ??
                  Border.all(
                    color: ColorPalette.gray300.withOpacity(0.3),
                    width: 1,
                  ),
            ),
            child: isEmpty
                ? Text(
                    placeholder ?? 'No option selected',
                    style: customTextStyle ??
                        LexendTextStyles.lexend14Regular.copyWith(
                          color: ColorPalette.placeHolderTextColor,
                        ),
                  )
                : _buildContent(selectedOption),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(RadioDisplayOption selectedOption) {
    switch (displayStyle) {
      case DisplayStyle.simple:
        return _buildSimpleContent(selectedOption);
      case DisplayStyle.detailed:
        return _buildDetailedContent(selectedOption);
      case DisplayStyle.badge:
        return _buildBadgeContent(selectedOption);
      case DisplayStyle.card:
        return _buildCardContent(selectedOption);
    }
  }

  Widget _buildSimpleContent(RadioDisplayOption selectedOption) {
    return Row(
      children: [
        if (showIcon && selectedOption.icon != null) ...[
          selectedOption.icon!,
          const SizedBox(width: 8),
        ],
        Expanded(
          child: Text(
            selectedOption.label,
            style: customTextStyle ??
                LexendTextStyles.lexend14Regular.copyWith(
                  color: ColorPalette.white,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedContent(RadioDisplayOption selectedOption) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showIcon && selectedOption.icon != null) ...[
          selectedOption.icon!,
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                selectedOption.label,
                style: customTextStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (showDescription && selectedOption.description != null)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    selectedOption.description!,
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                ),
            ],
          ),
        ),
        Icon(
          Icons.radio_button_checked,
          size: 20,
          color: selectedOption.color ?? ColorPalette.white,
        ),
      ],
    );
  }

  Widget _buildBadgeContent(RadioDisplayOption selectedOption) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: (selectedOption.color ?? ColorPalette.white).withOpacity(0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: selectedOption.color ?? ColorPalette.white,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon && selectedOption.icon != null) ...[
              selectedOption.icon!,
              const SizedBox(width: 6),
            ],
            Text(
              selectedOption.label,
              style: customTextStyle ??
                  LexendTextStyles.lexend12Medium.copyWith(
                    color: selectedOption.color ?? ColorPalette.white,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardContent(RadioDisplayOption selectedOption) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ColorPalette.darkToneInk.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: selectedOption.color ?? ColorPalette.white,
          width: 2,
        ),
      ),
      child: Row(
        children: [
          if (showIcon && selectedOption.icon != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: (selectedOption.color ?? ColorPalette.white).withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: selectedOption.icon!,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  selectedOption.label,
                  style: customTextStyle ??
                      LexendTextStyles.lexend14Medium.copyWith(
                        color: ColorPalette.white,
                      ),
                ),
                if (showDescription && selectedOption.description != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      selectedOption.description!,
                      style: LexendTextStyles.lexend12Regular.copyWith(
                        color: ColorPalette.placeHolderTextColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Icon(
            Icons.check_circle,
            color: selectedOption.color ?? ColorPalette.white,
          ),
        ],
      ),
    );
  }
}

enum DisplayStyle {
  simple,
  detailed,
  badge,
  card,
}
