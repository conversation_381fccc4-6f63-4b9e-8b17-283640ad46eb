import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';
import 'package:this_mobile_component/core/components/widgets/this_phone_input.dart';

/// Display format for phone output widgets
enum PhoneDisplayFormat {
  full,        // +****************
  national,    // (*************
  international, // ****** 123 4567
  minimal,     // 5551234567
}

/// A customizable phone output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays phone number data with various formatting and interaction options
class ThisPhoneOutput extends StatelessWidget {
  final String id;
  final String label;
  final String value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool selectable;
  final bool clickable;
  final String? helpText;
  final PhoneDisplayFormat displayFormat;
  final bool showIcon;
  final bool showFlag;
  final bool showValidationIcon;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool validatePhone;

  const ThisPhoneOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.selectable = true,
    this.clickable = false,
    this.helpText,
    this.displayFormat = PhoneDisplayFormat.full,
    this.showIcon = true,
    this.showFlag = true,
    this.showValidationIcon = false,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.validatePhone = true,
  });

  // Use the same countries list from phone input
  static const List<PhoneCountry> _countries = [
    PhoneCountry(name: 'United States', code: 'US', dialCode: '+1', flag: '🇺🇸', format: '(XXX) XXX-XXXX'),
    PhoneCountry(name: 'United Kingdom', code: 'GB', dialCode: '+44', flag: '🇬🇧', format: 'XXXX XXX XXXX'),
    PhoneCountry(name: 'Canada', code: 'CA', dialCode: '+1', flag: '🇨🇦', format: '(XXX) XXX-XXXX'),
    PhoneCountry(name: 'Australia', code: 'AU', dialCode: '+61', flag: '🇦🇺', format: 'XXXX XXX XXX'),
    PhoneCountry(name: 'Germany', code: 'DE', dialCode: '+49', flag: '🇩🇪', format: 'XXX XXXXXXXX'),
    PhoneCountry(name: 'France', code: 'FR', dialCode: '+33', flag: '🇫🇷', format: 'XX XX XX XX XX'),
    PhoneCountry(name: 'India', code: 'IN', dialCode: '+91', flag: '🇮🇳', format: 'XXXXX XXXXX'),
    PhoneCountry(name: 'China', code: 'CN', dialCode: '+86', flag: '🇨🇳', format: 'XXX XXXX XXXX'),
    PhoneCountry(name: 'Japan', code: 'JP', dialCode: '+81', flag: '🇯🇵', format: 'XXX-XXXX-XXXX'),
    PhoneCountry(name: 'Brazil', code: 'BR', dialCode: '+55', flag: '🇧🇷', format: '(XX) XXXXX-XXXX'),
    PhoneCountry(name: 'Mexico', code: 'MX', dialCode: '+52', flag: '🇲🇽', format: 'XXX XXX XXXX'),
    PhoneCountry(name: 'Spain', code: 'ES', dialCode: '+34', flag: '🇪🇸', format: 'XXX XX XX XX'),
    PhoneCountry(name: 'Italy', code: 'IT', dialCode: '+39', flag: '🇮🇹', format: 'XXX XXX XXXX'),
    PhoneCountry(name: 'Netherlands', code: 'NL', dialCode: '+31', flag: '🇳🇱', format: 'XX XXX XXXX'),
    PhoneCountry(name: 'South Korea', code: 'KR', dialCode: '+82', flag: '🇰🇷', format: 'XXX-XXXX-XXXX'),
  ];

  PhoneCountry? _getCountryFromPhone(String phone) {
    if (!phone.startsWith('+')) return null;
    
    return _countries.firstWhere(
      (country) => phone.startsWith(country.dialCode),
      orElse: () => _countries.first,
    );
  }

  String _extractPhoneNumber(String fullValue) {
    final country = _getCountryFromPhone(fullValue);
    if (country == null) return fullValue;

    if (fullValue.startsWith(country.dialCode)) {
      return fullValue.substring(country.dialCode.length).trim();
    }

    return fullValue;
  }

  bool _isValidPhone(String phone) {
    if (phone.isEmpty) return false;
    
    // Basic phone validation - should start with + and contain only valid characters
    final phoneRegex = RegExp(r'^\+[1-9]\d{1,14}$|^[0-9\s\-\(\)\.]{7,15}$');
    return phoneRegex.hasMatch(phone.replaceAll(RegExp(r'[\s\-\(\)\.]'), ''));
  }

  String _formatPhone(String phone) {
    if (phone.isEmpty) return '';

    final country = _getCountryFromPhone(phone);
    final phoneNumber = _extractPhoneNumber(phone);
    
    switch (displayFormat) {
      case PhoneDisplayFormat.full:
        return phone; // Return as-is
      case PhoneDisplayFormat.national:
        return phoneNumber.isNotEmpty ? phoneNumber : phone;
      case PhoneDisplayFormat.international:
        if (country != null) {
          return '${country.dialCode} ${phoneNumber.replaceAll(RegExp(r'[\s\-\(\)]'), ' ').trim()}';
        }
        return phone;
      case PhoneDisplayFormat.minimal:
        return phone.replaceAll(RegExp(r'[\s\-\(\)\+\.]'), '');
    }
  }

  void _copyToClipboard(BuildContext context) {
    if (value.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: value));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Phone number copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  Future<void> _launchPhone(BuildContext context) async {
    if (value.isEmpty || !_isValidPhone(value)) return;

    final phoneUri = Uri(
      scheme: 'tel',
      path: value.replaceAll(RegExp(r'[\s\-\(\)]'), ''),
    );

    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Cannot open phone dialer',
                style: LexendTextStyles.lexend12Regular.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              backgroundColor: const Color(0xFFC73E1D),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error opening phone dialer',
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.white,
              ),
            ),
            backgroundColor: const Color(0xFFC73E1D),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        );
      }
    }
  }

  Widget? _getValidationIcon() {
    if (!showValidationIcon || value.isEmpty) return null;

    final isValid = validatePhone ? _isValidPhone(value) : true;
    return Icon(
      isValid ? Icons.check_circle : Icons.error,
      size: 16,
      color: isValid ? ColorPalette.green : const Color(0xFFC73E1D),
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = value.isEmpty ? (placeholder ?? '') : _formatPhone(value);
    final isEmpty = value.isEmpty;
    final isValid = validatePhone ? _isValidPhone(value) : true;
    final country = _getCountryFromPhone(value);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: customLabelStyle ?? LexendTextStyles.lexend14Medium.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        
        // Content Container
        GestureDetector(
          onTap: onTap ?? (clickable ? () => _launchPhone(context) : null),
          child: Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withValues(alpha: 0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ?? Border.all(
                color: validatePhone && !isEmpty
                    ? (isValid ? ColorPalette.green.withValues(alpha: 0.3) : const Color(0xFFC73E1D).withValues(alpha: 0.3))
                    : ColorPalette.gray300.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Country Flag
                if (showFlag && country != null) ...[
                  Text(country.flag, style: const TextStyle(fontSize: 20)),
                  const SizedBox(width: 8),
                ],
                
                // Prefix Icon or Phone Icon
                if (prefixIcon != null) ...[
                  prefixIcon!,
                  const SizedBox(width: 8),
                ] else if (showIcon) ...[
                  Icon(
                    Icons.phone,
                    size: 16,
                    color: isEmpty 
                        ? ColorPalette.placeHolderTextColor 
                        : (isValid ? ColorPalette.white : const Color(0xFFC73E1D)),
                  ),
                  const SizedBox(width: 8),
                ],
                
                // Phone Content
                Expanded(
                  child: selectable
                      ? SelectableText(
                          displayValue,
                          style: customTextStyle ?? LexendTextStyles.lexend14Regular.copyWith(
                            color: isEmpty 
                                ? ColorPalette.placeHolderTextColor 
                                : (isValid ? ColorPalette.white : const Color(0xFFC73E1D)),
                            decoration: clickable && !isEmpty ? TextDecoration.underline : null,
                          ),
                        )
                      : Text(
                          displayValue,
                          style: customTextStyle ?? LexendTextStyles.lexend14Regular.copyWith(
                            color: isEmpty 
                                ? ColorPalette.placeHolderTextColor 
                                : (isValid ? ColorPalette.white : const Color(0xFFC73E1D)),
                            decoration: clickable && !isEmpty ? TextDecoration.underline : null,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                ),
                
                // Validation Icon
                if (_getValidationIcon() != null) ...[
                  const SizedBox(width: 8),
                  _getValidationIcon()!,
                ],
                
                // Copy Button
                if (copyable && value.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Copy phone number to clipboard',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],
                
                // Call Button
                if (clickable && value.isNotEmpty && isValid) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.call, size: 16),
                    onPressed: () => _launchPhone(context),
                    color: ColorPalette.placeHolderTextColor,
                    tooltip: 'Call this number',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: const EdgeInsets.all(4),
                  ),
                ],
                
                // Suffix Icon
                if (suffixIcon != null) ...[
                  const SizedBox(width: 8),
                  suffixIcon!,
                ],
              ],
            ),
          ),
        ),
        
        // Country info
        if (!isEmpty && country != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              country.name,
              style: LexendTextStyles.lexend12Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          ),
      ],
    );
  }
}

/// A specialized version of ThisPhoneOutput for displaying multiple phone numbers
class ThisPhoneListOutput extends StatelessWidget {
  final String id;
  final String label;
  final List<String> values;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool clickable;
  final String? helpText;
  final PhoneDisplayFormat displayFormat;
  final bool showIcon;
  final bool showFlag;
  final int? maxDisplayItems;
  final String moreItemsText;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;

  const ThisPhoneListOutput({
    super.key,
    required this.id,
    required this.label,
    required this.values,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.clickable = false,
    this.helpText,
    this.displayFormat = PhoneDisplayFormat.full,
    this.showIcon = true,
    this.showFlag = true,
    this.maxDisplayItems,
    this.moreItemsText = 'more',
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    final displayValues = maxDisplayItems != null && values.length > maxDisplayItems!
        ? values.take(maxDisplayItems!).toList()
        : values;
    
    final hasMore = maxDisplayItems != null && values.length > maxDisplayItems!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (showLabel) ...[
          Row(
            children: [
              Text(
                label,
                style: LexendTextStyles.lexend14Medium.copyWith(
                  color: ColorPalette.white,
                ),
              ),
              if (helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],
        
        // Phone List
        if (values.isEmpty)
          Container(
            width: double.infinity,
            padding: padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor ?? ColorPalette.darkToneInk.withValues(alpha: 0.3),
              borderRadius: borderRadius ?? BorderRadius.circular(6),
              border: border ?? Border.all(
                color: ColorPalette.gray300.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              placeholder ?? 'No phone numbers',
              style: LexendTextStyles.lexend14Regular.copyWith(
                color: ColorPalette.placeHolderTextColor,
              ),
            ),
          )
        else
          Column(
            children: [
              ...displayValues.asMap().entries.map((entry) {
                final index = entry.key;
                final phone = entry.value;
                return Padding(
                  padding: EdgeInsets.only(bottom: index < displayValues.length - 1 ? 8 : 0),
                  child: ThisPhoneOutput(
                    id: '${id}_$index',
                    label: '',
                    value: phone,
                    showLabel: false,
                    copyable: copyable,
                    clickable: clickable,
                    displayFormat: displayFormat,
                    showIcon: showIcon,
                    showFlag: showFlag,
                    padding: padding,
                    backgroundColor: backgroundColor,
                    borderRadius: borderRadius,
                    border: border,
                  ),
                );
              }),
              if (hasMore)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    '+${values.length - maxDisplayItems!} $moreItemsText',
                    style: LexendTextStyles.lexend12Regular.copyWith(
                      color: ColorPalette.placeHolderTextColor,
                    ),
                  ),
                ),
            ],
          ),
      ],
    );
  }
}
