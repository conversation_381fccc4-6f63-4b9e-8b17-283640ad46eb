# Custom Flutter Input Widgets

This directory contains custom Flutter input widgets following the `this_componentName_input` naming convention for data entry components.

## Naming Convention

All widgets follow the pattern: `this_componentName_input`
- `this` - Fixed prefix
- `componentName` - The component type (Text, Email, Phone, Number, Checkbox, Radio, etc.)
- `input` - All widgets are for data entry

## Available Input Widgets

### Input Widgets (Data Entry)

#### 1. ThisTextInput
- **File**: `this_text_input.dart`
- **Purpose**: Single-line text input with validation
- **Features**: Character count, clear button, custom validation, auto-focus
- **Usage**:
```dart
ThisTextInput(
  id: 'user_name',
  label: 'Full Name',
  value: nameValue,
  onChanged: (value) => setState(() => nameValue = value),
  required: true,
  maxLength: 50,
  showCharacterCount: true,
)
```

#### 2. ThisTextareaInput
- **File**: `this_textarea_input.dart`
- **Purpose**: Multi-line text input with validation
- **Features**: Expandable, word count, character count, custom validation
- **Usage**:
```dart
ThisTextareaInput(
  id: 'description',
  label: 'Description',
  value: descriptionValue,
  onChanged: (value) => setState(() => descriptionValue = value),
  minLines: 3,
  maxLines: 6,
  showCharacterCount: true,
)
```

#### 3. ThisEmailInput
- **File**: `this_email_input.dart`
- **Purpose**: Email input with comprehensive validation
- **Features**: Domain validation, international support, plus addressing, real-time validation
- **Usage**:
```dart
ThisEmailInput(
  id: 'user_email',
  label: 'Email Address',
  value: emailValue,
  onChanged: (value) => setState(() => emailValue = value),
  required: true,
  allowedDomains: ['gmail.com', 'company.com'],
  showValidationIcon: true,
)
```

#### 4. ThisPhoneInput
- **File**: `this_phone_input.dart`
- **Purpose**: Phone number input with country selection
- **Features**: Country flags, formatting, extension support, international validation
- **Usage**:
```dart
ThisPhoneInput(
  id: 'user_phone',
  label: 'Phone Number',
  value: phoneValue,
  onChanged: (value) => setState(() => phoneValue = value),
  required: true,
  defaultCountry: 'US',
  showFlag: true,
  allowExtensions: true,
)
```

#### 5. ThisNumberInput
- **File**: `this_number_input.dart`
- **Purpose**: Numeric input with formatting and validation
- **Features**: Currency support, decimals, thousands separators, range validation
- **Usage**:
```dart
ThisNumberInput(
  id: 'amount',
  label: 'Amount',
  value: numberValue,
  onChanged: (value) => setState(() => numberValue = value),
  required: true,
  currency: '\$',
  decimals: 2,
  thousandsSeparator: true,
  min: 0,
  max: 10000,
)
```

#### 6. ThisCheckboxInput
- **File**: `this_checkbox_input.dart`
- **Purpose**: Multiple selection checkbox input
- **Features**: Select all option, min/max selection limits, grid layout
- **Usage**:
```dart
ThisCheckboxInput(
  id: 'preferences',
  label: 'Preferences',
  options: checkboxOptions,
  value: selectedValues,
  onChanged: (values) => setState(() => selectedValues = values),
  allowSelectAll: true,
  minSelected: 1,
)
```

#### 7. ThisRadioInput
- **File**: `this_radio_input.dart`
- **Purpose**: Single selection radio input
- **Features**: Button style option, descriptions, grid layout
- **Usage**:
```dart
ThisRadioInput(
  id: 'gender',
  label: 'Gender',
  options: radioOptions,
  value: selectedValue,
  onChanged: (value) => setState(() => selectedValue = value),
  required: true,
)
```

#### 8. ThisYearInput
- **File**: `this_year_input.dart`
- **Purpose**: Year selection input
- **Features**: Dropdown or text input, year range validation, future/past restrictions
- **Usage**:
```dart
ThisYearInput(
  id: 'birth_year',
  label: 'Birth Year',
  value: yearValue,
  onChanged: (value) => setState(() => yearValue = value),
  showDropdown: true,
  minYear: 1950,
  maxYear: 2024,
)
```

#### 9. ThisMonthInput
- **File**: `this_month_input.dart`
- **Purpose**: Month selection input
- **Features**: Full/short/numeric display, allowed months restriction
- **Usage**:
```dart
ThisMonthInput(
  id: 'birth_month',
  label: 'Birth Month',
  value: monthValue,
  onChanged: (value) => setState(() => monthValue = value),
  displayFormat: MonthDisplayFormat.full,
)
```

#### 10. ThisDayInput
- **File**: `this_day_input.dart`
- **Purpose**: Day selection input
- **Features**: Month-aware validation, leap year support, dropdown option
- **Usage**:
```dart
ThisDayInput(
  id: 'birth_day',
  label: 'Birth Day',
  value: dayValue,
  onChanged: (value) => setState(() => dayValue = value),
  month: monthValue,
  year: yearValue,
  showDropdown: true,
)
```

#### 11. ThisTimeInput
- **File**: `this_time_input.dart`
- **Purpose**: Time selection input
- **Features**: 12/24 hour format, time picker, time range validation
- **Usage**:
```dart
ThisTimeInput(
  id: 'meeting_time',
  label: 'Meeting Time',
  value: timeValue,
  onChanged: (value) => setState(() => timeValue = value),
  use24HourFormat: false,
  showTimePicker: true,
)
```



## Common Features

### All Input Widgets Support:
- Required field validation
- Custom validation functions
- Disabled and read-only states
- Help text with tooltips
- Error message display
- Real-time validation
- Consistent theming
- Parameter-driven configuration
- Accessibility support
- Auto-focus capabilities

## Usage

1. Import the widgets:
```dart
import 'package:this_mobile_component/core/components/widgets/widgets.dart';
```

2. Use in your Flutter widgets following the examples above.

3. See `example_usage.dart` for a comprehensive demonstration of all widgets.

## Theming

All widgets use the app's theme system defined in:
- `ColorPalette` for colors
- `LexendTextStyles` for typography
- `AppTheme` for overall theming

## Validation

Input widgets support multiple validation levels:
1. Required field validation
2. Format validation (pattern matching)
3. Range validation (min/max values)
4. Custom validation functions
5. Real-time and on-blur validation
