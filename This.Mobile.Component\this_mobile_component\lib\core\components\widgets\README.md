# Custom Flutter Widgets

This directory contains custom Flutter widgets following the `this_componentName_relatedTo` naming convention for interchange-related components.

## Naming Convention

All widgets follow the pattern: `this_componentName_relatedTo`
- `this` - Fixed prefix
- `componentName` - The component type (Text, Checkbox, Radio, etc.)
- `relatedTo` - Either `input` (for data entry) or `output` (for data display)

## Available Widgets

### Input Widgets (Data Entry)

#### 1. ThisTextInput
- **File**: `this_text_input.dart`
- **Purpose**: Single-line text input with validation
- **Features**: Character count, clear button, custom validation, auto-focus
- **Usage**:
```dart
ThisTextInput(
  id: 'user_name',
  label: 'Full Name',
  value: nameValue,
  onChanged: (value) => setState(() => nameValue = value),
  required: true,
  maxLength: 50,
  showCharacterCount: true,
)
```

#### 2. ThisTextareaInput
- **File**: `this_textarea_input.dart`
- **Purpose**: Multi-line text input with validation
- **Features**: Expandable, word count, character count, custom validation
- **Usage**:
```dart
ThisTextareaInput(
  id: 'description',
  label: 'Description',
  value: descriptionValue,
  onChanged: (value) => setState(() => descriptionValue = value),
  minLines: 3,
  maxLines: 6,
  showCharacterCount: true,
)
```

#### 3. ThisCheckboxInput
- **File**: `this_checkbox_input.dart`
- **Purpose**: Multiple selection checkbox input
- **Features**: Select all option, min/max selection limits, grid layout
- **Usage**:
```dart
ThisCheckboxInput(
  id: 'preferences',
  label: 'Preferences',
  options: checkboxOptions,
  value: selectedValues,
  onChanged: (values) => setState(() => selectedValues = values),
  allowSelectAll: true,
  minSelected: 1,
)
```

#### 4. ThisRadioInput
- **File**: `this_radio_input.dart`
- **Purpose**: Single selection radio input
- **Features**: Button style option, descriptions, grid layout
- **Usage**:
```dart
ThisRadioInput(
  id: 'gender',
  label: 'Gender',
  options: radioOptions,
  value: selectedValue,
  onChanged: (value) => setState(() => selectedValue = value),
  required: true,
)
```

#### 5. ThisYearInput
- **File**: `this_year_input.dart`
- **Purpose**: Year selection input
- **Features**: Dropdown or text input, year range validation, future/past restrictions
- **Usage**:
```dart
ThisYearInput(
  id: 'birth_year',
  label: 'Birth Year',
  value: yearValue,
  onChanged: (value) => setState(() => yearValue = value),
  showDropdown: true,
  minYear: 1950,
  maxYear: 2024,
)
```

#### 6. ThisMonthInput
- **File**: `this_month_input.dart`
- **Purpose**: Month selection input
- **Features**: Full/short/numeric display, allowed months restriction
- **Usage**:
```dart
ThisMonthInput(
  id: 'birth_month',
  label: 'Birth Month',
  value: monthValue,
  onChanged: (value) => setState(() => monthValue = value),
  displayFormat: MonthDisplayFormat.full,
)
```

#### 7. ThisDayInput
- **File**: `this_day_input.dart`
- **Purpose**: Day selection input
- **Features**: Month-aware validation, leap year support, dropdown option
- **Usage**:
```dart
ThisDayInput(
  id: 'birth_day',
  label: 'Birth Day',
  value: dayValue,
  onChanged: (value) => setState(() => dayValue = value),
  month: monthValue,
  year: yearValue,
  showDropdown: true,
)
```

#### 8. ThisTimeInput
- **File**: `this_time_input.dart`
- **Purpose**: Time selection input
- **Features**: 12/24 hour format, time picker, time range validation
- **Usage**:
```dart
ThisTimeInput(
  id: 'meeting_time',
  label: 'Meeting Time',
  value: timeValue,
  onChanged: (value) => setState(() => timeValue = value),
  use24HourFormat: false,
  showTimePicker: true,
)
```

### Output Widgets (Data Display)

#### 1. ThisTextOutput
- **File**: `this_text_output.dart`
- **Purpose**: Display text data
- **Features**: Copy to clipboard, selectable text, custom formatting
- **Usage**:
```dart
ThisTextOutput(
  id: 'display_name',
  label: 'Name',
  value: nameValue,
  copyable: true,
  prefixIcon: Icon(Icons.person),
)
```

#### 2. ThisTextareaOutput
- **File**: `this_textarea_output.dart`
- **Purpose**: Display multi-line text data
- **Features**: Expandable, word count, copy to clipboard
- **Usage**:
```dart
ThisTextareaOutput(
  id: 'display_description',
  label: 'Description',
  value: descriptionValue,
  expandable: true,
  showWordCount: true,
)
```

#### 3. ThisCheckboxOutput
- **File**: `this_checkbox_output.dart`
- **Purpose**: Display selected checkbox values
- **Features**: Multiple display formats (list, chips, badges, inline)
- **Usage**:
```dart
ThisCheckboxOutput(
  id: 'selected_preferences',
  label: 'Selected Preferences',
  options: displayOptions,
  value: selectedValues,
  displayFormat: DisplayFormat.chips,
)
```

#### 4. ThisRadioOutput
- **File**: `this_radio_output.dart`
- **Purpose**: Display selected radio value
- **Features**: Multiple display styles (simple, detailed, badge, card)
- **Usage**:
```dart
ThisRadioOutput(
  id: 'selected_gender',
  label: 'Gender',
  options: displayOptions,
  value: selectedValue,
  displayStyle: DisplayStyle.detailed,
)
```

#### 5. ThisYearOutput
- **File**: `this_year_output.dart`
- **Purpose**: Display year data
- **Features**: Relative time info, multiple formats, year ranges
- **Usage**:
```dart
ThisYearOutput(
  id: 'display_year',
  label: 'Year',
  value: yearValue,
  showRelativeInfo: true,
  copyable: true,
)
```

#### 6. ThisMonthOutput
- **File**: `this_month_output.dart`
- **Purpose**: Display month data
- **Features**: Full/short/numeric display, relative month info
- **Usage**:
```dart
ThisMonthOutput(
  id: 'display_month',
  label: 'Month',
  value: monthValue,
  displayFormat: MonthDisplayFormat.full,
  showMonthInfo: true,
)
```

#### 7. ThisDayOutput
- **File**: `this_day_output.dart`
- **Purpose**: Display day data
- **Features**: Ordinal format, weekday info, day ranges
- **Usage**:
```dart
ThisDayOutput(
  id: 'display_day',
  label: 'Day',
  value: dayValue,
  displayFormat: DayDisplayFormat.ordinal,
  showDayInfo: true,
)
```

#### 8. ThisTimeOutput
- **File**: `this_time_output.dart`
- **Purpose**: Display time data
- **Features**: Multiple formats, relative time, time ranges
- **Usage**:
```dart
ThisTimeOutput(
  id: 'display_time',
  label: 'Time',
  value: timeValue,
  displayFormat: TimeDisplayFormat.DESCRIPTIVE,
  showRelativeTime: true,
)
```

## Common Features

### All Input Widgets Support:
- Required field validation
- Custom validation functions
- Disabled and read-only states
- Help text with tooltips
- Error message display
- Real-time validation
- Consistent theming

### All Output Widgets Support:
- Optional labels
- Help text with tooltips
- Custom styling options
- Consistent theming
- Tap callbacks
- Placeholder text for empty values

## Usage

1. Import the widgets:
```dart
import 'package:this_mobile_component/core/components/widgets/widgets.dart';
```

2. Use in your Flutter widgets following the examples above.

3. See `example_usage.dart` for a comprehensive demonstration of all widgets.

## Theming

All widgets use the app's theme system defined in:
- `ColorPalette` for colors
- `LexendTextStyles` for typography
- `AppTheme` for overall theming

## Validation

Input widgets support multiple validation levels:
1. Required field validation
2. Format validation (pattern matching)
3. Range validation (min/max values)
4. Custom validation functions
5. Real-time and on-blur validation
