import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:this_mobile_component/core/theme/color_palette.dart';
import 'package:this_mobile_component/core/theme/text_styles.dart';

/// A customizable textarea output widget following the 'this_componentName_relatedTo' naming convention
/// This widget displays multi-line text data with various formatting and interaction options
class ThisTextareaOutput extends StatefulWidget {
  final String id;
  final String label;
  final String value;
  final String? placeholder;
  final bool showLabel;
  final bool copyable;
  final bool selectable;
  final String? helpText;
  final int? maxLines;
  final int minLines;
  final bool expandable;
  final bool showExpandButton;
  final TextAlign textAlign;
  final TextStyle? customTextStyle;
  final TextStyle? customLabelStyle;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool showWordCount;

  const ThisTextareaOutput({
    super.key,
    required this.id,
    required this.label,
    required this.value,
    this.placeholder,
    this.showLabel = true,
    this.copyable = false,
    this.selectable = true,
    this.helpText,
    this.maxLines,
    this.minLines = 3,
    this.expandable = true,
    this.showExpandButton = true,
    this.textAlign = TextAlign.start,
    this.customTextStyle,
    this.customLabelStyle,
    this.prefixIcon,
    this.suffixIcon,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.showWordCount = false,
  });

  @override
  State<ThisTextareaOutput> createState() => _ThisTextareaOutputState();
}

class _ThisTextareaOutputState extends State<ThisTextareaOutput> {
  bool _isExpanded = false;

  void _copyToClipboard(BuildContext context) {
    if (widget.value.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: widget.value));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Copied to clipboard',
            style: LexendTextStyles.lexend12Regular.copyWith(
              color: ColorPalette.white,
            ),
          ),
          backgroundColor: ColorPalette.darkToneInk,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
      );
    }
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  int _getWordCount(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }

  int _getLineCount(String text) {
    if (text.isEmpty) return 0;
    return text.split('\n').length;
  }

  @override
  Widget build(BuildContext context) {
    final displayValue = widget.value.isEmpty ? (widget.placeholder ?? '') : widget.value;
    final isEmpty = widget.value.isEmpty;
    final lineCount = _getLineCount(widget.value);
    final shouldShowExpandButton = widget.showExpandButton && widget.expandable && lineCount > widget.minLines;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        if (widget.showLabel) ...[
          Row(
            children: [
              Text(
                widget.label,
                style: widget.customLabelStyle ??
                    LexendTextStyles.lexend14Medium.copyWith(
                      color: ColorPalette.white,
                    ),
              ),
              if (widget.helpText != null) ...[
                const SizedBox(width: 4),
                Tooltip(
                  message: widget.helpText!,
                  child: Icon(
                    Icons.info_outline,
                    size: 16,
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
        ],

        // Content Container
        GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              minHeight: widget.minLines * 20.0, // Approximate line height
            ),
            padding: widget.padding ?? const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? ColorPalette.darkToneInk.withValues(alpha: 0.3),
              borderRadius: widget.borderRadius ?? BorderRadius.circular(6),
              border: widget.border ??
                  Border.all(
                    color: ColorPalette.gray300.withValues(alpha: 0.3),
                    width: 1,
                  ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with icons
                if (widget.prefixIcon != null || widget.copyable || widget.suffixIcon != null)
                  Row(
                    children: [
                      if (widget.prefixIcon != null) ...[
                        widget.prefixIcon!,
                        const SizedBox(width: 8),
                      ],
                      const Spacer(),
                      if (widget.copyable && widget.value.isNotEmpty) ...[
                        IconButton(
                          icon: const Icon(Icons.copy, size: 16),
                          onPressed: () => _copyToClipboard(context),
                          color: ColorPalette.placeHolderTextColor,
                          tooltip: 'Copy to clipboard',
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                          padding: const EdgeInsets.all(4),
                        ),
                      ],
                      if (widget.suffixIcon != null) ...[
                        const SizedBox(width: 8),
                        widget.suffixIcon!,
                      ],
                    ],
                  ),

                // Text Content
                widget.selectable
                    ? SelectableText(
                        displayValue,
                        style: widget.customTextStyle ??
                            LexendTextStyles.lexend14Regular.copyWith(
                              color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                            ),
                        maxLines: _isExpanded ? null : (widget.maxLines ?? widget.minLines),
                        textAlign: widget.textAlign,
                      )
                    : Text(
                        displayValue,
                        style: widget.customTextStyle ??
                            LexendTextStyles.lexend14Regular.copyWith(
                              color: isEmpty ? ColorPalette.placeHolderTextColor : ColorPalette.white,
                            ),
                        maxLines: _isExpanded ? null : (widget.maxLines ?? widget.minLines),
                        overflow: _isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
                        textAlign: widget.textAlign,
                      ),

                // Expand/Collapse Button
                if (shouldShowExpandButton)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: GestureDetector(
                      onTap: _toggleExpanded,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _isExpanded ? Icons.expand_less : Icons.expand_more,
                            size: 16,
                            color: ColorPalette.placeHolderTextColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _isExpanded ? 'Show less' : 'Show more',
                            style: LexendTextStyles.lexend12Regular.copyWith(
                              color: ColorPalette.placeHolderTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),

        // Word and character count
        if (widget.showWordCount && widget.value.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Words: ${_getWordCount(widget.value)}',
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
                Text(
                  'Characters: ${widget.value.length}',
                  style: LexendTextStyles.lexend12Regular.copyWith(
                    color: ColorPalette.placeHolderTextColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
